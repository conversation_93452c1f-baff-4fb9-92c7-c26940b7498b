worker_processes  4;

error_log  /var/log/nginx/error.log warn;
pid        /var/log/nginx/nginx.pid;

worker_rlimit_nofile 8192;

events {
  worker_connections  1024;
}

http {
  include    /etc/nginx/mime.types;
  include    /etc/nginx/proxy.conf;
  include    /etc/nginx/conf.d/upstream*.conf;
  include    /etc/nginx/conf.d/web.conf*;

  default_type application/octet-stream;
  log_format   main '$remote_addr - $remote_user [$time_local]  $status '
    '"$request" $body_bytes_sent "$http_referer" '
    '"$http_user_agent" "$http_x_forwarded_for"';
    
  limit_req_zone $remote_addr zone=api:1m rate=4r/s;

server {

  listen 80 default_server;
  server_name _;
  
  error_page 404 /404-nodomain.html;
  location = /404-nodomain.html {
    root /usr/share/nginx/html;
    internal;
  }

}
 
server {
      listen       80;
      server_name  localhost; #Server domain
      access_log   /var/log/nginx/hollaex.access.log  main;

      include    /etc/nginx/conf.d/plugin*.conf;

      location /api-docs {
        proxy_pass      http://api;
      }

      location /docs {
        proxy_pass      http://api;
      }

      location /v2/order {
        limit_req zone=api burst=10 nodelay;
        limit_req_log_level info;
        limit_req_status 429;

        proxy_pass      http://api;
      }

      location / {
        limit_req zone=api burst=10 nodelay;
        limit_req_log_level info;
        limit_req_status 429;

        proxy_pass      http://api;
      }

      location /explorer {
        proxy_pass      http://api;
      }

      location /stream {
        proxy_http_version  1.1;
        proxy_set_header    Upgrade $http_upgrade;
        proxy_set_header    Connection "upgrade";

        proxy_pass      http://socket;
      }

      location /plugins {
        proxy_pass      http://plugins;
      }

      error_page 404 /404.html;
      location = /404.html {
        root /usr/share/nginx/html;
        internal;
      }

      error_page 429 /429.html;
      location = /429.html {
        root /usr/share/nginx/html;
        internal;
      }
    }

}


