// Generated by Selenium IDE
const { Builder, By, Key, until } = require('selenium-webdriver')
const assert = require('assert');
const testContext = require ('../onboarding/Newuser')
const { expect } = require('chai');
const { Console } = require('console');

const path = require('path')
const logPath = path.join(__dirname, './.log',path.basename(__filename,'.js'));
const reportPath = path.join(__dirname, './../Report',path.dirname(__filename).replace(path.dirname(__dirname),''),path.basename(__filename,'.js'));
const util = require ('../Utils/Utils.js');
util.makeReportDir(reportPath);
util.makeReportDir(logPath);
require('console-stamp')(console, { 
	format: ':date(yyyy/mm/dd HH:MM:ss.l)|' 
} );
const { findSafariDriver } = require('selenium-webdriver/safari');
require('dotenv').config({ path: path.resolve(__dirname, '../.env') })
let userName = process.env.ADMIN_USER;
let passWord = process.env.ADMIN_PASS;
let logInPage = process.env.LOGIN_PAGE;
let password = process.env.PASSWORD;
let website = process.env.WEBSITE;
describe('g', function() {
	this.timeout(300000)
	let driver
	let vars
	function obj(array) {
		let newObject = {}
   
		for (let i=0; i < array.length/3; i++) {
			let j = i*3
			if (array[i] !== undefined) {
				newObject[array[j]] = [array[j],array[j+1],array[j+2]]
			}
		}
		return newObject 
	};
	beforeEach(async function() {
		driver = await new Builder().forBrowser('chrome').build()
		vars = {}
	})
	afterEach(async function() {
		// await driver.quit();
	})
	it('User0 is Maker And User1 is taker', async function() {
		// Test name: g
		// Step # | name | target | value
		// 1 | open | https://sandbox.hollaex.com/login | 
		await driver.get("https://sandbox.hollaex.com/login")
		// 2 | executeScript | return [] | Users
		vars["Users"] = await driver.executeScript("return []")
		// 3 | executeScript | return "user" | user
		vars["user"] = await driver.executeScript("return \"user\"")
		// 4 | executeScript | return 0 | counter
		vars["counter"] = await driver.executeScript("return 0")
		// 5 | times | 3 | 
		const times = 3
		for(let i = 0; i < times; i++) {
			// 6 | executeScript | return ${user}+${counter}+"@testsae.com" | newUser
			vars["newUser"] = await driver.executeScript("return arguments[0]+arguments[1]+\"@testsae.com\"", vars["user"],vars["counter"])
			// 7 | executeScript | return ${Users}.concat(${newUser}) | Users
			vars["Users"] = await driver.executeScript("return arguments[0].concat(arguments[1])", vars["Users"],vars["newUser"])
			// 8 | executeScript | return eval(${counter} + 1) | counter
			vars["counter"] = await driver.executeScript("return eval(arguments[0] + 1)", vars["counter"])
			// 9 | echo | ${Users} | 
			console.log(vars["Users"][i])
			// 10 | end |  | 
		}
		await driver.sleep(5000)
		// 11 | type | name=email | <EMAIL>
		await driver.findElement(By.name("email")).sendKeys("<EMAIL>")
		// 12 | type | name=password |
		await driver.findElement(By.name("password")).sendKeys(password)
		// 13 | click | css=.holla-button | 
		await driver.findElement(By.css(".holla-button")).click()
		await driver.sleep(5000)
		// 14 | storeText | css=.trader-account-wrapper .summary-block-title | Level
		vars["Level"] = await driver.findElement(By.css(".trader-account-wrapper .summary-block-title")).getText()
		//15 | storeText | css=.trade-account-secondary-txt > .d-flex > div:nth-child(2) | Fee
		vars["Fee"] = await driver.findElement(By.css(".trade-account-secondary-txt > .d-flex > div:nth-child(2)")).getText()
		// 16 | echo | ${Fee}+" and "+${Level} | 
		console.log(vars["Fee"]+" and "+ vars["Level"] )
		// 17 | open | trade/xht-usdt | 
		await driver.get("https://sandbox.hollaex.com/trade/xht-usdt")
		// 18 | pause | 5000 | 
		await driver.sleep(5000)
		// 19 | storeText | css=.trade-col_side_wrapper > .trade_block-wrapper:nth-child(1) | Q
		let words = vars["Q"] = await driver.findElement(By.css(".trade-col_side_wrapper > .trade_block-wrapper:nth-child(1)")).getText()
		// 20 | storeText | css=.trade_orderbook-market-price | the latest price
		vars["the latest price"] = await driver.findElement(By.css(".trade_orderbook-market-price")).getText()
		// 21 | echo | ${Q}+"and"+${the latest price} | 
		console.log(vars["Q"]+"and"+vars["the latest price"] )
		// 22 | executeScript | return ${Q}.split(" ") | R
		vars["R"] = await driver.executeScript("return arguments[0].split('Seller')", vars["Q"])
		// 23 | echo | R | 
		//console.log(vars["R"])
		let Bwords = words.split("\n")
		console.log(Bwords.indexOf('spread'))
		let sell = Bwords.slice(10,Bwords.indexOf('spread')-1)
		let buy = Bwords.slice(Bwords.indexOf('spread')+2,Bwords.length-1)
		let spread = [Bwords[Bwords.indexOf('spread')-1],Bwords[Bwords.indexOf('spread')+1]]
		console.log(Bwords)
   
		console.log(buy.length)
		console.log(sell.length)
		console.log("selllll :"+ sell)
		console.log("buyyyyy :"+ buy)
		// let newObject = obj(sell)
  
		console.log(obj(sell))
		console.log(obj(buy))
		console.log(spread)
		let fring = Number(0.01)//eval(spread[1]*0.5)
		let newOrderPriceBuy = (Number(await driver.findElement(By.css(".fill-bid:nth-child(1) > .d-flex > .trade_orderbook-cell-price")).getText())+fring).toFixed(2);
		let newOrderPriceSell = (Number(await driver.findElement(By.css(".fill-bid:nth-child(1) > .d-flex > .trade_orderbook-cell-price")).getText())+Number(spread[1]-fring)).toFixed(2);
		console.log(newOrderPriceBuy+"\n"+newOrderPriceSell)
		if (fring<spread[1] & obj(sell)[newOrderPriceBuy] == undefined & obj(sell)[newOrderPriceBuy] == undefined){
			console.log('newOrderPriceBuy:'+newOrderPriceBuy)
		}
		if (eval(spread[1]-fring)<spread[1] &obj(sell)[newOrderPriceSell] == undefined & obj(sell)[newOrderPriceSell] == undefined){
			console.log('newOrderPriceSell:'+newOrderPriceSell)
		}

		await driver.sleep(4000);
		// 24 | click | css=.text-center:nth-child(2) | 
		// LIMIT
		await driver.findElement(By.css(".text-center:nth-child(2)")).click()
		// 25 | click | css=.holla-button-font:nth-child(2) | 
		// sell
		await driver.findElement(By.css(".holla-button-font:nth-child(2)")).click()
		// 26 | click | css=.holla-button-font:nth-child(1) | 
		// buy
		//await driver.findElement(By.css(".holla-button-font:nth-child(1)")).click()
		
	
		// 24 | click | name=price | 
		await driver.findElement(By.name("price")).click()
		await driver.findElement(By.name("price")).clear()
		// 25 | type | name=price | newOrderPriceSell
		await driver.findElement(By.name("price")).sendKeys(newOrderPriceSell)
		// 26 | sendKeys | name=price | ${KEY_ENTER}
		// 10 | click | name=size | 
		await driver.findElement(By.name('size')).click();
		// 11 | type | name=size | 1
		await driver.findElement(By.name('size')).sendKeys('1');
		// 12 | click | css=.holla-button | 

		
		console.log(' 11 | click | css=.holla-button | ');


		await driver.findElement(By.css('.holla-button')).click();
		// 13 | click | css=.text-capitalize | 
		await driver.findElement(By.css('.text-capitalize')).click();
		// 14 | assertText | css=.text-capitalize | Limit Sell
		assert(await driver.findElement(By.css('.text-capitalize')).getText() == 'Limit Sell');
		// 15 | click | css=.notification-content-information > .d-flex:nth-child(2) > .f-1:nth-child(2) | 
		await driver.findElement(By.css('.notification-content-information > .d-flex:nth-child(2) > .f-1:nth-child(2)')).click();
		// 16 | assertText | css=.notification-content-information > .d-flex:nth-child(2) > .f-1:nth-child(2) | 1 XHT
		assert(await driver.findElement(By.css('.notification-content-information > .d-flex:nth-child(2) > .f-1:nth-child(2)')).getText() == '1 XHT');
		// 17 | click | css=.notification-content-information > .d-flex:nth-child(3) > .f-1:nth-child(2) | 
		await driver.findElement(By.css('.notification-content-information > .d-flex:nth-child(3) > .f-1:nth-child(2)')).click();
		// 18 | assertText | css=.notification-content-information > .d-flex:nth-child(3) > .f-1:nth-child(2) | 1 USDT
		assert(await driver.findElement(By.css('.notification-content-information > .d-flex:nth-child(3) > .f-1:nth-child(2)')).getText() == newOrderPriceSell+' USDT');
		// 19 | click | css=.d-flex > .holla-button:nth-child(3) | 
		await driver.sleep(1000)
		await driver.findElement(By.css('.d-flex > .holla-button:nth-child(3)')).click();
		
		util.hollatimestamp();
		console.log("Timestamp : "+String(util.getHollatimestamp()));
		await driver.sleep(3000)
		vars["newPrice"] = await driver.findElement(By.css(".fill-ask:nth-child(1) > .d-flex > .trade_orderbook-cell-price")).getText()
		expect(vars['newPrice']).to.equal(newOrderPriceSell);
		await driver.sleep(4000)
		// 20 | click | css=.table_body-row:nth-child(1) .action_notification-text | 
		//	await driver.findElement(By.css('.table_body-row:nth-child(1) .action_notification-text')).click();

	
		// LIMIT
		await driver.findElement(By.css(".text-center:nth-child(2)")).click()
		// 25 | click | css=.holla-button-font:nth-child(2) | 
		// sell
		// await driver.findElement(By.css(".holla-button-font:nth-child(2)")).click()
		// 26 | click | css=.holla-button-font:nth-child(1) | 
		// buy
		await driver.findElement(By.css(".holla-button-font:nth-child(1)")).click()
		
	
		// 24 | click | name=price | 
		await driver.findElement(By.name("price")).click()
		await driver.findElement(By.name("price")).clear()
		// 25 | type | name=price | newOrderPriceSell
		await driver.findElement(By.name("price")).sendKeys(newOrderPriceBuy)
		// 26 | sendKeys | name=price | ${KEY_ENTER}
		// 10 | click | name=size | 
		await driver.findElement(By.name('size')).click();
		await driver.findElement(By.name("size")).clear()
		// 11 | type | name=size | 1
		await driver.findElement(By.name('size')).sendKeys('1');
		// 12 | click | css=.holla-button | 

		

		await driver.findElement(By.css('.holla-button')).click();
		// 13 | click | css=.text-capitalize | 
		await driver.findElement(By.css('.text-capitalize')).click();
		// 14 | assertText | css=.text-capitalize | Limit Sell
		assert(await driver.findElement(By.css('.text-capitalize')).getText() == 'Limit Buy');
		// 15 | click | css=.notification-content-information > .d-flex:nth-child(2) > .f-1:nth-child(2) | 
		await driver.findElement(By.css('.notification-content-information > .d-flex:nth-child(2) > .f-1:nth-child(2)')).click();
		// 16 | assertText | css=.notification-content-information > .d-flex:nth-child(2) > .f-1:nth-child(2) | 1 XHT
		assert(await driver.findElement(By.css('.notification-content-information > .d-flex:nth-child(2) > .f-1:nth-child(2)')).getText() == '1 XHT');
		// 17 | click | css=.notification-content-information > .d-flex:nth-child(3) > .f-1:nth-child(2) | 
		await driver.findElement(By.css('.notification-content-information > .d-flex:nth-child(3) > .f-1:nth-child(2)')).click();
		// 18 | assertText | css=.notification-content-information > .d-flex:nth-child(3) > .f-1:nth-child(2) | 1 USDT
		assert(await driver.findElement(By.css('.notification-content-information > .d-flex:nth-child(3) > .f-1:nth-child(2)')).getText() == newOrderPriceBuy+' USDT');
		// 19 | click | css=.d-flex > .holla-button:nth-child(3) | 
		await driver.sleep(1000)
		await driver.findElement(By.css('.d-flex > .holla-button:nth-child(3)')).click();
		
		util.hollatimestamp();
		console.log("Timestamp : "+String(util.getHollatimestamp()));
		await driver.sleep(3000)
		vars["newPriceBuy"] = await driver.findElement(By.css(".fill-bid:nth-child(1) > .d-flex > .trade_orderbook-cell-price")).getText()
		expect(Number(vars['newPriceBuy'])).to.equal(Number(newOrderPriceBuy));
		await driver.sleep(4000)
		// 20 | click | css=.table_body-row:nth-child(1) .action_notification-text | 
		//await driver.findElement(By.css('.table_body-row:nth-child(1) .action_notification-text')).click();

		
		// 22 | click | css=.app-bar-account-content > div:nth-child(2) | 
		await driver.findElement(By.css(".app-bar-account-content > div:nth-child(2)")).click()
		// 23 | click | css=.app-bar-account-menu-list:nth-child(10) > .edit-wrapper__container:nth-child(3) | 
		await driver.findElement(By.css(".app-bar-account-menu-list:nth-child(10) > .edit-wrapper__container:nth-child(3)")).click()
		////////////////////////////////////////////////
  
		await driver.sleep(5000)
		// 11 | type | name=email | <EMAIL>
		await driver.findElement(By.name("email")).sendKeys("<EMAIL>")
		// 12 | type | name=password |
		await driver.findElement(By.name("password")).sendKeys(password)
		// 13 | click | css=.holla-button | 
		await driver.findElement(By.css(".holla-button")).click()
		await driver.sleep(5000)
		// 14 | storeText | css=.trader-account-wrapper .summary-block-title | Level
		vars["Level"] = await driver.findElement(By.css(".trader-account-wrapper .summary-block-title")).getText()
		//15 | storeText | css=.trade-account-secondary-txt > .d-flex > div:nth-child(2) | Fee
		vars["Fee"] = await driver.findElement(By.css(".trade-account-secondary-txt > .d-flex > div:nth-child(2)")).getText()
		// 16 | echo | ${Fee}+" and "+${Level} | 
		console.log(vars["Fee"]+" and "+ vars["Level"] )
		// 17 | open | trade/xht-usdt | 
		await driver.get("https://sandbox.hollaex.com/trade/xht-usdt")
		// 18 | pause | 5000 | 
  	await driver.sleep(4000);
		// 24 | click | css=.text-center:nth-child(2) | 
		// LIMIT
		await driver.findElement(By.css(".text-center:nth-child(2)")).click()
		// 25 | click | css=.holla-button-font:nth-child(2) | 
		// sell
		await driver.findElement(By.css(".holla-button-font:nth-child(2)")).click()
		// 26 | click | css=.holla-button-font:nth-child(1) | 
		// buy
		//await driver.findElement(By.css(".holla-button-font:nth-child(1)")).click()
		
	
		// 24 | click | name=price | 
		await driver.findElement(By.name("price")).click()
		await driver.findElement(By.name("price")).clear()
		// 25 | type | name=price | newOrderPriceSell
		await driver.findElement(By.name("price")).sendKeys(newOrderPriceBuy)
		// 26 | sendKeys | name=price | ${KEY_ENTER}
		// 10 | click | name=size | 
		await driver.findElement(By.name('size')).click();
		// 11 | type | name=size | 1
		await driver.findElement(By.name('size')).sendKeys('1');
		// 12 | click | css=.holla-button | 

		
		console.log(' 11 | click | css=.holla-button | ');


		await driver.findElement(By.css('.holla-button')).click();
		// 13 | click | css=.text-capitalize | 
		await driver.findElement(By.css('.text-capitalize')).click();
		// 14 | assertText | css=.text-capitalize | Limit Sell
		assert(await driver.findElement(By.css('.text-capitalize')).getText() == 'Limit Sell');
		// 15 | click | css=.notification-content-information > .d-flex:nth-child(2) > .f-1:nth-child(2) | 
		await driver.findElement(By.css('.notification-content-information > .d-flex:nth-child(2) > .f-1:nth-child(2)')).click();
		// 16 | assertText | css=.notification-content-information > .d-flex:nth-child(2) > .f-1:nth-child(2) | 1 XHT
		assert(await driver.findElement(By.css('.notification-content-information > .d-flex:nth-child(2) > .f-1:nth-child(2)')).getText() == '1 XHT');
		// 17 | click | css=.notification-content-information > .d-flex:nth-child(3) > .f-1:nth-child(2) | 
		await driver.findElement(By.css('.notification-content-information > .d-flex:nth-child(3) > .f-1:nth-child(2)')).click();
		// 18 | assertText | css=.notification-content-information > .d-flex:nth-child(3) > .f-1:nth-child(2) | 1 USDT
		assert(await driver.findElement(By.css('.notification-content-information > .d-flex:nth-child(3) > .f-1:nth-child(2)')).getText() == newOrderPriceBuy+' USDT');
		// 19 | click | css=.d-flex > .holla-button:nth-child(3) | 
		await driver.sleep(1000)
		await driver.findElement(By.css('.d-flex > .holla-button:nth-child(3)')).click();
		
		util.hollatimestamp();
		console.log("Timestamp : "+String(util.getHollatimestamp()));
		await driver.sleep(3000)

		// LIMIT
		await driver.findElement(By.css(".text-center:nth-child(2)")).click()
		// 25 | click | css=.holla-button-font:nth-child(2) | 
		// sell
		// await driver.findElement(By.css(".holla-button-font:nth-child(2)")).click()
		// 26 | click | css=.holla-button-font:nth-child(1) | 
		// buy
		await driver.findElement(By.css(".holla-button-font:nth-child(1)")).click()
		
	
		// 24 | click | name=price | 
		await driver.findElement(By.name("price")).click()
		await driver.findElement(By.name("price")).clear()
		// 25 | type | name=price | newOrderPriceSell
		await driver.findElement(By.name("price")).sendKeys(newOrderPriceSell)
		// 26 | sendKeys | name=price | ${KEY_ENTER}
		// 10 | click | name=size | 
		await driver.findElement(By.name('size')).click();
		await driver.findElement(By.name("size")).clear()
		// 11 | type | name=size | 1
		await driver.findElement(By.name('size')).sendKeys('1');
		// 12 | click | css=.holla-button | 

		

		await driver.findElement(By.css('.holla-button')).click();
		// 13 | click | css=.text-capitalize | 
		await driver.findElement(By.css('.text-capitalize')).click();
		// 14 | assertText | css=.text-capitalize | Limit Sell
		assert(await driver.findElement(By.css('.text-capitalize')).getText() == 'Limit Buy');
		// 15 | click | css=.notification-content-information > .d-flex:nth-child(2) > .f-1:nth-child(2) | 
		await driver.findElement(By.css('.notification-content-information > .d-flex:nth-child(2) > .f-1:nth-child(2)')).click();
		// 16 | assertText | css=.notification-content-information > .d-flex:nth-child(2) > .f-1:nth-child(2) | 1 XHT
		assert(await driver.findElement(By.css('.notification-content-information > .d-flex:nth-child(2) > .f-1:nth-child(2)')).getText() == '1 XHT');
		// 17 | click | css=.notification-content-information > .d-flex:nth-child(3) > .f-1:nth-child(2) | 
		await driver.findElement(By.css('.notification-content-information > .d-flex:nth-child(3) > .f-1:nth-child(2)')).click();
		// 18 | assertText | css=.notification-content-information > .d-flex:nth-child(3) > .f-1:nth-child(2) | 1 USDT
		assert(await driver.findElement(By.css('.notification-content-information > .d-flex:nth-child(3) > .f-1:nth-child(2)')).getText() == newOrderPriceSell+' USDT');
		// 19 | click | css=.d-flex > .holla-button:nth-child(3) | 
		await driver.sleep(1000)
		await driver.findElement(By.css('.d-flex > .holla-button:nth-child(3)')).click();
		
		util.hollatimestamp();
		console.log("Timestamp : "+String(util.getHollatimestamp()));

		await driver.sleep(4000)
		// 20 | click | css=.table_body-row:nth-child(1) .action_notification-text | 
		//await driver.findElement(By.css('.table_body-row:nth-child(1) .action_notification-text')).click();

		
		// 22 | click | css=.app-bar-account-content > div:nth-child(2) | 
		// await driver.findElement(By.css(".app-bar-account-content > div:nth-child(2)")).click()
		// 23 | click | css=.app-bar-account-menu-list:nth-child(10) > .edit-wrapper__container:nth-child(3) | 
		//  await driver.findElement(By.css(".app-bar-account-menu-list:nth-child(10) > .edit-wrapper__container:nth-child(3)")).click()
		////////////////////////////////////////////////

		await driver.get(website + 'transactions' );
		await driver.manage().window().maximize() ;
		await driver.sleep(10000);
		//await driver.findElement(By.css(".trade_block-wrapper:nth-child(2) .action_notification-text")).click()
   
		// 2 | click | css=.table_body-row:nth-child(1) > td:nth-child(7) | 
		await driver.findElement(By.css('.table_body-row:nth-child(1) > td:nth-child(7)')).click();
		// 3 | storeText | css=.table_body-row:nth-child(1) > td:nth-child(7) | timestamp
		vars['timestamp'] = await driver.findElement(By.css('.table_body-row:nth-child(1) > td:nth-child(7)')).getText();
		// 4 | click | css=.table_body-row:nth-child(1) > .text-uppercase | 
		await driver.findElement(By.css('.table_body-row:nth-child(1) > .text-uppercase')).click();
		// 5 | storeText | css=.table_body-row:nth-child(1) > .text-uppercase | Pair
		vars['Pair'] = await driver.findElement(By.css('.table_body-row:nth-child(1) > .text-uppercase')).getText();
		// 6 | click | css=.table_body-row:nth-child(1) | 
		await driver.findElement(By.css('.table_body-row:nth-child(1)')).click();
		// 7 | storeText | css=.table_body-row:nth-child(1) .buy | side
		vars['side'] = await driver.findElement(By.css('.table_body-row:nth-child(1) .buy')).getText();
		// 8 | click | css=.table_body-row:nth-child(1) > td:nth-child(3) | 
		await driver.findElement(By.css('.table_body-row:nth-child(1) > td:nth-child(3)')).click();
		// 9 | storeText | css=.table_body-row:nth-child(1) > td:nth-child(3) | size
		vars['size'] = await driver.findElement(By.css('.table_body-row:nth-child(1) > td:nth-child(3)')).getText();
		// 10 | click | css=.table_body-row:nth-child(1) > td:nth-child(4) | 
		await driver.findElement(By.css('.table_body-row:nth-child(1) > td:nth-child(4)')).click();
		// 11 | storeText | css=.table_body-row:nth-child(1) > td:nth-child(4) | price
		vars['price'] = await driver.findElement(By.css('.table_body-row:nth-child(1) > td:nth-child(4)')).getText();
		// 12 | click | css=.table_body-row:nth-child(1) > td:nth-child(5) | 
		await driver.findElement(By.css('.table_body-row:nth-child(1) > td:nth-child(5)')).click();
		// 13 | storeText | css=.table_body-row:nth-child(1) > td:nth-child(5) | amount
		vars['amount'] = await driver.findElement(By.css('.table_body-row:nth-child(1) > td:nth-child(5)')).getText();
		// 14 | click | css=.table_body-row:nth-child(1) | 
		await driver.findElement(By.css('.table_body-row:nth-child(1)')).click();
		// 15 | storeText | css=.table_body-row:nth-child(1) > td:nth-child(6) | fee
		vars['fee'] = await driver.findElement(By.css('.table_body-row:nth-child(1) > td:nth-child(6)')).getText();
		// 16 | echo | ${Pair},${timestamp},${side},${size},${price},${amount},${fee}}} | 
		console.log(vars['Pair'],vars['timestamp'],vars['side'],vars['size'],vars['price'],vars['amount'],vars['fee']);
		console.log(vars['timestamp']+" should be "+util.getHollatimestamp());
		expect(vars['timestamp']).to.equal(util.getHollatimestamp());

	});

})