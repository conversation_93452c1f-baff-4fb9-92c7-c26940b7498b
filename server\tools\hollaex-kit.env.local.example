DEPLOYMENT_MODE=all

API_HOST=localhost
API_NAME=hollaex-kit
DOMAIN=http://localhost:3000
NETWORK=testnet

ACTIVATION_CODE=

NATIVE_CURRENCY=

CURRENCIES=xht,usdt,btc,eth,bch,xrp
DEFAULT_THEME=dark
EMAILS_TIMEZONE=UTC
LOGO_IMAGE=https://s3.ap-northeast-2.amazonaws.com/public-holla-images/bitholla-logo.png
NEW_USER_DEFAULT_LANGUAGE=en
NEW_USER_IS_ACTIVATED=true
NODE_ENV=development
PAIRS=xht-usdt

PORT=10010
WEBSOCKET_PORT=10080

PUBSUB_HOST=hollaex-kit-redis
PUBSUB_PORT=6379
PUBSUB_PASSWORD=bitholla
REDIS_HOST=hollaex-kit-redis
REDIS_PORT=6379
REDIS_PASSWORD=bitholla

SENDER_EMAIL=<EMAIL>

SUPPORT_EMAIL=<EMAIL>
VALID_LANGUAGES=en


ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=

ISSUER=bitholla
SECRET=secret
CAPTCHA_SECRET_KEY=
CAPTCHA_SITE_KEY=

ADMIN_WHITELIST_IP=

DB_HOST=hollaex-kit-db
DB_NAME=hollaex
DB_PASSWORD=root
DB_PORT=5432
DB_USERNAME=admin
DB_DIALECT=postgres
DB_SSL=false

FRESHDESK_HOST=
FRESHDESK_AUTH=
FRESHDESK_KEY=

ZENDESK_HOST=
ZENDESK_KEY=

ID_DOCS_BUCKET=
S3_ACCESSKEYID=
S3_SECRETACCESSKEY=
SES_ACCESSKEYID=
SES_REGION=
SES_SECRETACCESSKEY=
SNS_ACCESSKEYID=
SNS_REGION=
SNS_SECRETACCESSKEY=

SMTP_SERVER=
SMTP_PORT=
SMTP_USER=
SMTP_PASSWORD=

PLUGINS=

SEND_EMAIL_TO_SUPPORT=
ALLOWED_DOMAINS=

API_KEY=
API_SECRET=

KIT_VERSION=