worker_processes  4;

error_log  /var/log/error.log warn;
pid        /var/log/nginx.pid;

worker_rlimit_nofile 8192;

events {
  worker_connections  1024;
}

http {
  include    /etc/nginx/mime.types;
  include    /etc/nginx/proxy.conf;
  include    /etc/nginx/conf.d/upstream.conf;

  default_type application/octet-stream;
  log_format   main '$remote_addr - $remote_user [$time_local]  $status '
    '"$request" $body_bytes_sent "$http_referer" '
    '"$http_user_agent" "$http_x_forwarded_for"';
  
  log_format   filter '$remote_addr - $remote_user [$time_local]  $status '
    '"$req_filtered" $body_bytes_sent "$http_referer" '
    '"$http_user_agent" "$http_x_forwarded_for"';
    
  limit_req_zone $remote_addr zone=api:1m rate=2r/s;
  limit_req_zone $remote_addr zone=order:1m rate=5r/s;

  #Server blockes will be automatically generated
 
server {
      listen       80;
      server_name  hollaex;

      set $req_filtered $request;
      if ($req_filtered ~ (.*)key=[^&]*(.*)) { 
          set $req_filtered $1key=****$2;
      }
      if ($req_filtered ~ (.*)secret=[^&]*(.*)) { 
          set $req_filtered $1secret=****$2;
      }

      access_log   /var/log/hollaex.access.log  filter;

      include    /etc/nginx/conf.d/plugin*.conf;

      location /explorer {
        proxy_pass      http://api;
      }

      location /v2/order {
        proxy_pass      http://api;
      }

      location /v2 {
        proxy_pass      http://api;
      }

	  	location /plugins {
        proxy_pass      http://plugins;
      }

      location /stream {
        proxy_http_version  1.1;
        proxy_set_header    Upgrade $http_upgrade;
        proxy_set_header    Connection "upgrade";

        proxy_pass      http://socket;
      }

      location / {
        proxy_pass      http://api;
      }

      error_page 429 /429.html;
      location = /429.html {
        root /usr/share/nginx/html;
        internal;
      }
    }
  }

