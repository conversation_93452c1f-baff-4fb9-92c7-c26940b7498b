version: '3'
services:
  hollaex-kit-redis:
    image: redis:5.0.5-alpine
    depends_on:
      - hollaex-kit-db
    networks:
      - hollaex-kit-network
    ports:
      - 6379:6379
    environment:
      - REDIS_PASSWORD=bitholla
    command : ["sh", "-c", "redis-server --requirepass $${REDIS_PASSWORD}"]
  hollaex-kit-db:
    image: postgres:10.9
    ports:
      - 5432:5432
    environment:
      - POSTGRES_DB=hollaex
      - POSTGRES_USER=admin
      - POSTGRES_PASSWORD=root
    networks:
      - hollaex-kit-network
  hollaex-kit-server:
    image: hollaex-kit-server
    ports:
      - 10010:10010
      - 10011:10011
    build:
      context: .
      dockerfile: ./tools/Dockerfile.pm2
    env_file:
      - ./tools/hollaex-kit.env.local
    entrypoint:
      - pm2-runtime
      - start
      - ecosystem.config.js
      - --env
      - development
    volumes:
      - ./api:/app/api
      - ./config:/app/config
      - ./db:/app/db
      - ./plugins.js:/app/plugins.js
      - ./mail:/app/mail
      - ./ws:/app/ws
      - ./app.js:/app/app.js
      - ./ecosystem.config.js:/app/ecosystem.config.js
      - ./constants.js:/app/constants.js
      - ./messages.js:/app/messages.js
      - ./logs:/app/logs
      - ./test:/app/test
      - ./tools:/app/tools
      - ./utils:/app/utils
      - ./init.js:/app/init.js
    depends_on:
      - hollaex-kit-db
      - hollaex-kit-redis
    networks:
      - hollaex-kit-network
  hollaex-kit-nginx:
    image: nginx:1.15.8-alpine
    volumes:
      - ./tools/nginx:/etc/nginx
      - ./tools/nginx/conf.d:/etc/nginx/conf.d
      - ./tools/nginx/logs:/var/log
      - ./tools/nginx/static:/usr/share/nginx/html
    ports:
      - 80:80
    environment:
      - NGINX_PORT=80
    depends_on:
      - hollaex-kit-server
    networks:
      - hollaex-kit-network

networks:
  hollaex-kit-network: