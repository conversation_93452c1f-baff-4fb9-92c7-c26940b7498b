// Generated by Selenium IDE
async function SmartTrade(){
	const { Builder, By, Key, until } = require('selenium-webdriver');
	const assert = require('assert');
	const { expect } = require('chai');
	const path = require('path')
	const logPath = path.join(__dirname, './.log',path.basename(__filename,'.js'));
	const reportPath = path.join(__dirname, './../Report',path.dirname(__filename).replace(path.dirname(__dirname),''),path.basename(__filename,'.js'));
	const util = require ('../Utils/Utils.js');
	util.makeReportDir(reportPath);
	util.makeReportDir(logPath);
	require('console-stamp')(console, { 
		format: ':date(yyyy/mm/dd HH:MM:ss.l)|' 
	} );
	require('dotenv').config({ path: path.resolve(__dirname, '../.env') })
	let adminUser = process.env.ADMIN_USER;
	let passWord = process.env.ADMIN_PASS;
	let website = process.env.WEBSITE;
	let step =util.getStep();
	describe('smartTrade', function() {
		this.timeout(300000);
		let driver;
		let vars;
		function sleep(ms) {
			return new Promise((resolve) => {
				setTimeout(resolve, ms);
			});
		}
		beforeEach(async function() {
			driver = await new Builder().forBrowser('chrome').build();
			vars = {};
			driver.manage().window().maximize();
	        util.kitLogIn(step,driver,adminUser,passWord);
   	});
		afterEach(async function() {
			util.setStep(step)
		// await driver.quit();
		});
		it('smartTrade', async function() {
    
			await sleep(15000);
			console.log(' Test name: smartTrade');
			console.log(' Step # | name | target | value');
		
			console.log(step++,'  | open | /trade/xht-usdt | ');
			await driver.get(website+ 'trade/xht-usdt');
			await sleep(5000);
		
			console.log(step++,'  | click | css=.trade-col_side_wrapper > .trade_block-wrapper:nth-child(1) | ');
			await driver.findElement(By.css('.text-center:nth-child(1)')).click();
		
			console.log(step++,'  | click | css=.trade-col_side_wrapper > .trade_block-wrapper:nth-child(1) | ');
			await driver.findElement(By.css('.trade-col_side_wrapper > .trade_block-wrapper:nth-child(1)')).click();
			await sleep(5000);

			console.log(step++,'  | type | name=size| 1');
			await driver.findElement(By.name('size')).clear();
			await driver.findElement(By.name('size')).sendKeys('1');
		
			console.log(step++,'  | storeText | css=.trade-col_side_wrapper > .trade_block-wrapper:nth-child(1) .trade_block-title-currency-xht | pair');
			vars['pair'] = await driver.findElement(By.css('.trade-col_side_wrapper > .trade_block-wrapper:nth-child(1) .trade_block-title-currency-xht')).getText();
		
			console.log(step++,'  | echo | ${pair} | ');
			console.log('The Pair is: '+vars['pair']);
		
			console.log(step++,'  | click | css=.text-center:nth-child(1) | ');
			await driver.findElement(By.css('.text-center:nth-child(1)')).click();
			await sleep(5000);
		
			vars['USDT AMOUNT'] = await driver.findElement(By.css('.accordion_section:nth-child(1) .wallet_section-title-amount')).getText();
		
			console.log(step++,' ) .wallet_section-title-amount | HXT AMOUNT');
			vars['HXT AMOUNT'] = await driver.findElement(By.css('.accordion_section:nth-child(2) .wallet_section-title-amount')).getText();
		
			console.log(step++,'  | echo | ${USDT AMOUNT}, ${XHT AMOUNT} | ');
			console.log('${USDT AMOUNT} : '+vars['USDT AMOUNT'], '${XHT AMOUNT} : '+vars['HXT AMOUNT']);
		
			console.log(step++,'  | storeValue | name=size | size');
			vars['size'] = await driver.findElement(By.name('size')).getAttribute('value');
    
			console.log(step++,'  | click | css=.trade-col_action_wrapper > .trade_block-wrapper:nth-child(1)');
			vars['market'] = await driver.findElement(By.css('.text-center:nth-child(1)')).getText();
		
			console.log(step++,'  | echo | ${market} | ');
			console.log('Market/Limit: '+vars['market']);
		
			console.log(step++,'  | click | css=.trade_orderbook-spread-text | ');
			await driver.findElement(By.css('.trade_orderbook-spread-text')).click();
		
			console.log(step++,' | storeText | css=.trade_orderbook-spread-text | spread');
			vars['spread'] = await driver.findElement(By.css('.trade_orderbook-spread-text')).getText();
		
			console.log(step++,'  | echo | ${spread} | ');
			console.log('Spread: '+vars['spread']);
		
			console.log(step++,' | type | name=size | 1');
			let coinSize = parseInt(vars['size'],10);
			vars['Balance'] = await driver.findElement(By.css('div:nth-child(2) > .blue-link:nth-child(1)')).getText();
			let walletBalance = parseInt(vars['Balance'] ,10);
			vars['EstimatedPrice'] = await driver.findElement(By.css('.d-flex:nth-child(1) > .text-price')).getText();
			let estimatedSize = parseInt(vars['EstimatedPrice'] ,10);
			while (estimatedSize < walletBalance) {
				coinSize = coinSize+(10);

				await driver.findElement(By.name('size')).clear();
				await driver.findElement(By.name('size')).sendKeys(String(coinSize));
				console.log('Size is: '+String(coinSize));
				await driver.findElement(By.css('.trade-col_action_wrapper > .trade_block-wrapper:nth-child(1)')).click();
				vars['Balance'] = await driver.findElement(By.css('div:nth-child(2) > .blue-link:nth-child(1)')).getText();
				vars['EstimatedPrice'] = await driver.findElement(By.css('.d-flex:nth-child(1) > .text-price')).getText();
				estimatedSize = parseInt(vars['EstimatedPrice'] ,10);
				console.log('Wallet Balance: '+vars['Balance']);
				console.log('Estimated Price: '+vars['EstimatedPrice']);
			// {
			// 	const elements = await driver.findElements(By.css('.form-error'));
			// 	assert(!elements.length);
			// }
			//console.log(' 21 | verifyNotText | css=.form-error | Insufficient balance');
			// {
			//   const text = await driver.findElement(By.css(".form-error")).getText()
			//   assert(text !== "Insufficient balance")
			// }
			}
			console.log(step++,'  | click | css=.justify-content-end > .pointer | ');
			await driver.findElement(By.css('.justify-content-end > .pointer')).click();
		
			console.log(step++,' | click | css=.text-center:nth-child(1) | ');
			await driver.findElement(By.css('.text-center:nth-child(1)')).click()
		
			console.log(step++,'  | click | css=div:nth-child(2) > .blue-link:nth-child(1) | ');
			await driver.findElement(By.css('div:nth-child(2) > .blue-link:nth-child(1)')).click()
		
			console.log(step++,'  | click | css=.trade-col_action_wrapper | ');
			await driver.findElement(By.css('.trade-col_action_wrapper')).click()
		
			console.log(step++,' ) > .text-price | BalanceEstimated');
			vars['BalanceEstimated'] = await driver.findElement(By.css('.d-flex:nth-child(1) > .text-price')).getText()
		
			console.log(step++,'  | storeValue | name=size | totalSize');
			vars['totalSize'] = await driver.findElement(By.name('size')).getAttribute('value')
		
			console.log(step++,'  | echo | ${BalanceEstimated},${totalSize} | ');
			console.log(vars['BalanceEstimated'] + ' should be less than ' + vars['totalSize'])
			let BalanceEstimated = parseFloat(vars['BalanceEstimated'])
			let totalSize = parseFloat(['totalSize'])
			expect(totalSize - BalanceEstimated).to.be.above(0);
		
			console.log('This is the EndOfTest');
		});
		it('1xht buying', async function() {
			console.log(' Test name: 1xht buying');
			console.log(' Step # | name | target | value');
			await sleep(15000);

			console.log(step++,'  | open | /trade/xht-usdt | ');
			await driver.get(website+ 'trade/xht-usdt');
			await sleep(5000);
		
			//itrating on elms
			let elms = await driver.findElements(By.className('f-1 trade_orderbook-cell trade_orderbook-cell_total pointer'));
			for (var elm in elms){}
			await driver.findElement(By.css('.text-center:nth-child(1)')).click();
		
			console.log(step++,'  | click | css=.trade-col_side_wrapper > .trade_block-wrapper:nth-child(1) | ');
			await driver.findElement(By.css('.trade-col_side_wrapper > .trade_block-wrapper:nth-child(1)')).click();
			await sleep(5000);
		
			console.log(step++,'  | storeText | css=.accordion_section:nth-child(1) .wallet_section-title-amount | USDT AMOUNT');
			vars['USDT AMOUNT'] = await driver.findElement(By.css('.accordion_section:nth-child(1) .wallet_section-title-amount')).getText();
			let USDTAmountBefore = parseFloat(vars['USDT AMOUNT']);
		
			console.log(step++,'  | storeText | css=.accordion_section:nth-child(2) .wallet_section-title-amount | HXT AMOUNT');
			vars['XHT AMOUNT'] = await driver.findElement(By.css('.accordion_section:nth-child(2) .wallet_section-title-amount')).getText();
			let XHTAmountBefore =parseFloat(vars['XHT AMOUNT']);
		
			console.log(step++,'  | echo | ${USDT AMOUNT}, ${XHT AMOUNT}');
			console.log('${USDT AMOUNT}:'+vars['USDT AMOUNT']+ ';${XHT AMOUNT} : '+vars['XHT AMOUNT']);
			console.log('USDT and XHT',String(XHTAmountBefore),typeof XHTAmountBefore,String(USDTAmountBefore),typeof USDTAmountBefore);
		
			console.log(step++,'  | click | css=.text-center:nth-child(1) | ');
			await driver.findElement(By.css('.text-center:nth-child(1)')).click();
		
			console.log(step++,'  | click | name=size | ');
			await driver.findElement(By.name('size')).click();
		
			console.log(step++,'  | type | name=size | 1');
			await driver.findElement(By.name('size')).clear();
			await driver.findElement(By.name('size')).sendKeys('1');
		
			console.log(step++,'  | click | css=.trade-col_action_wrapper > .trade_block-wrapper:nth-child(1) | ');
			await driver.findElement(By.css('.trade-col_action_wrapper > .trade_block-wrapper:nth-child(1)')).click();
		
			console.log(step++,'  | storeText | css=.d-flex:nth-child(1) > .text-price | USDT');
			vars['USDT'] = await driver.findElement(By.css('.d-flex:nth-child(1) > .text-price')).getText();
			let EstimatedPrice = parseFloat(vars['USDT']);
			console.log('EstimatedPrice',String(EstimatedPrice),typeof EstimatedPrice );
		
			console.log(step++,'  | echo | ${USDT} | ');
			console.log(vars['USDT']);
		
			console.log(step++,'  | click | css=.holla-button | ');
			await driver.findElement(By.css('.holla-button')).click();
		
			console.log(step++,'  | click | css=.notification-content-information > .d-flex:nth-child(1) | ');
			await driver.findElement(By.css('.notification-content-information > .d-flex:nth-child(1)')).click();
		
			console.log(step++,'  | verifyText | css=.text-capitalize | Market Buy');
			assert(await driver.findElement(By.css('.text-capitalize')).getText() == 'Market Buy');
		
			console.log(step++,'  | click | css=.notification-content-information > .d-flex:nth-child(2) | ');
			await driver.findElement(By.css('.notification-content-information > .d-flex:nth-child(2)')).click();
		
			console.log(step++,'  | verifyText | css=.notification-content-information > .d-flex:nth-child(2) > .f-1:nth-child(2) | 1 XHT');
			assert(await driver.findElement(By.css('.notification-content-information > .d-flex:nth-child(2) > .f-1:nth-child(2)')).getText() == '1 XHT');
		
			console.log(step++,'  | click | css=.d-flex > .holla-button:nth-child(3) | ');
			await driver.findElement(By.css('.d-flex > .holla-button:nth-child(3)')).click();
			util.hollatimestamp();
			console.log('Timestamp : '+String(util.getHollatimestamp()));
			await sleep(3000);

			console.log(step++,'  | click | css=.trade-col_action_wrapper > .f-1 | ');
			await driver.findElement(By.css('.trade-col_action_wrapper > .f-1')).click();
			await sleep(3000);

			console.log(step++,'  | storeText | css=.accordion_section:nth-child(1) .wallet_section-title-amount | USDTAFTER');
			vars['USDTAFTER'] = await driver.findElement(By.css('.accordion_section:nth-child(1) .wallet_section-title-amount')).getText();
			let USDTAmountAfter = parseFloat(vars['USDTAFTER']);
			console.log(vars['USDTAFTER'],typeof USDTAmountAfter,String(USDTAmountAfter));
			//console.log(' 20 | click | css=.accordion_section:nth-child(2) > .accordion_section_title | ');
   	    //await driver.findElement(By.css('.accordion_section:nth-child(2) > .accordion_section_title')).click();
		
			console.log(step++,'  | storeText | css=.accordion_section--open > .wallet_section-title-amount | XHTAFTER');
			vars['XHTAFTER'] = await driver.findElement(By.css('.accordion_section:nth-child(2) .wallet_section-title-amount')).getText();
			let XHTAmountAfter = parseFloat(vars['XHTAFTER']);
		
			console.log(step++,' | echo | ${XHTAFTER} | ');
			console.log(vars['XHTAFTER'],typeof XHTAmountAfter,String(XHTAmountAfter));
			console.log(vars['XHTAFTER']+' - '+vars['XHT AMOUNT'],String(XHTAmountBefore - XHTAmountAfter));
			console.log(vars['USDTAFTER']+' - '+vars['USDT AMOUNT'], String(USDTAmountBefore - USDTAmountAfter));

  
			console.log(step++,'  | open | transactions|');
			await driver.get(website + 'transactions' );
			await sleep(10000);
		   
			console.log(step++,'  | click | css=.table_body-row:nth-child(1) > td:nth-child(7) | ');
			await driver.findElement(By.css('.table_body-row:nth-child(1) > td:nth-child(7)')).click()
		
			console.log(step++,'  | storeText | css=.table_body-row:nth-child(1) > td:nth-child(7) | timestamp');
			vars['timestamp'] = await driver.findElement(By.css('.table_body-row:nth-child(1) > td:nth-child(7)')).getText()
		
			console.log(step++,'  | click | css=.table_body-row:nth-child(1) > .text-uppercase | ');
			await driver.findElement(By.css('.table_body-row:nth-child(1) > .text-uppercase')).click()
		
			console.log(step++,'  | storeText | css=.table_body-row:nth-child(1) > .text-uppercase | Pair');
			vars['Pair'] = await driver.findElement(By.css('.table_body-row:nth-child(1) > .text-uppercase')).getText()
		
			console.log(step++,'  | click | css=.table_body-row:nth-child(1) | ');
			await driver.findElement(By.css('.table_body-row:nth-child(1)')).click()
		
			console.log(step++,'  | storeText | css=.table_body-row:nth-child(1) .buy | side');
			vars['side'] = await driver.findElement(By.css('.table_body-row:nth-child(1) .buy')).getText()
		
			console.log(step++,'  | click | css=.table_body-row:nth-child(1) > td:nth-child(3) | ');
			await driver.findElement(By.css('.table_body-row:nth-child(1) > td:nth-child(3)')).click()
		
			console.log(step++,'  | storeText | css=.table_body-row:nth-child(1) > td:nth-child(3) | size');
			vars['size'] = await driver.findElement(By.css('.table_body-row:nth-child(1) > td:nth-child(3)')).getText()
		
			console.log(step++,'  | click | css=.table_body-row:nth-child(1) > td:nth-child(4) | ');
			await driver.findElement(By.css('.table_body-row:nth-child(1) > td:nth-child(4)')).click()
		
			console.log(step++,'  | storeText | css=.table_body-row:nth-child(1) > td:nth-child(4) | price');
			vars['price'] = await driver.findElement(By.css('.table_body-row:nth-child(1) > td:nth-child(4)')).getText()
		
			console.log(step++,'  | click | css=.table_body-row:nth-child(1) > td:nth-child(5) | ');
			await driver.findElement(By.css('.table_body-row:nth-child(1) > td:nth-child(5)')).click()
		
			console.log(step++,' | storeText | css=.table_body-row:nth-child(1) > td:nth-child(5) | amount');
			vars['amount'] = await driver.findElement(By.css('.table_body-row:nth-child(1) > td:nth-child(5)')).getText()
		
			console.log(step++,'  | click | css=.table_body-row:nth-child(1) | ');
			await driver.findElement(By.css('.table_body-row:nth-child(1)')).click()
		
			console.log(step++,'  | storeText | css=.table_body-row:nth-child(1) > td:nth-child(6) | fee');
			vars['fee'] = await driver.findElement(By.css('.table_body-row:nth-child(1) > td:nth-child(6)')).getText()
		
			console.log(step++,'  | echo | ${Pair},${timestamp},${side},${size},${price},${amount},${fee}}} | ');
			console.log(vars['Pair'],vars['timestamp'],vars['side'],vars['size'],vars['price'],vars['amount'],vars['fee'])
			expect(vars['timestamp']).to.equal(util.getHollatimestamp());
		
			console.log('This is the EndOfTest');
		});
  
	});
}
describe('Main Test', function () {
 
	SmartTrade();
})
module.exports.SmartTrade = SmartTrade;

