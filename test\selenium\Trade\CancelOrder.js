// Generated by Selenium IDE
async function CancelOrder(){
	const { Builder, By, Key, until } = require('selenium-webdriver');
	const assert = require('assert');
	const testContext = require ('./../onboarding/Newuser');
	const { expect } = require('chai');
	const { Console } = require('console');

	const path = require('path')
	const logPath = path.join(__dirname, './.log',path.basename(__filename,'.js'));
	const reportPath = path.join(__dirname, './../Report',path.dirname(__filename).replace(path.dirname(__dirname),''),path.basename(__filename,'.js'));
	const util = require ('../Utils/Utils.js');
	util.makeReportDir(reportPath);
	util.makeReportDir(logPath);
	require('console-stamp')(console, { 
		format: ':date(yyyy/mm/dd HH:MM:ss.l)|' 
	} );
	const { findSafariDriver } = require('selenium-webdriver/safari');
	require('dotenv').config({ path: path.resolve(__dirname, '../.env') });
	let userName = process.env.ADMIN_USER;
	let username = process.env.USER0;
	let passWord = process.env.ADMIN_PASS;
	let password = process.env.PASSWORD;
	let logInPage = process.env.LOGIN_PAGE;
	let website = process.env.WEBSITE;
	let step = util.getStep();

	describe('g', function() {
		this.timeout(300000);
		let driver;
		let vars;
		function obj(array) {
			let newObject = {};
   
			for (let i=0; i < array.length/3; i++) {
				let j = i*3;
				if (array[i] !== undefined) {
					newObject[array[j]] = [array[j],array[j+1],array[j+2]];
				}
			}
			return newObject; 
		};
		beforeEach(async function() {
			driver = await new Builder().forBrowser('chrome').build();
			vars = {};
			let step = util.getStep()
		});
		afterEach(async function() {
			util.setStep(step);
		// await driver.quit();
		});
		it('Cancel order', async function() {
			console.log(' Test name: g');
			console.log(' Step # | name | target | value');
			console.log(step++,'  | open | https://sandbox.hollaex.com/login | ');
			await driver.get(logInPage);
			console.log(step++,'  | executeScript | return [] | Users');
			console.log(step++,'  | end |  | ');
			await driver.sleep(5000);
			console.log(step++,'  | type | name=email | <EMAIL>');
			await driver.findElement(By.name('email')).sendKeys(username);
			console.log(step++,'  | type | name=password | password');
			await driver.findElement(By.name('password')).sendKeys(password);
			console.log(step++,'  | click | css=.holla-button | ');
			await driver.findElement(By.css('.holla-button')).click();
			await driver.sleep(5000);
			console.log(step++,'  | open | trade/xht-usdt | ');
			await driver.get(website+'trade/xht-usdt/');
			console.log(step++,' | pause | 5000 | ');
			await driver.sleep(5000);
			console.log(step++,' | storeText | css=.trade-col_side_wrapper > .trade_block-wrapper:nth-child(1) | Q');
			let words = vars['Q'] = await driver.findElement(By.css('.trade-col_side_wrapper > .trade_block-wrapper:nth-child(1)')).getText();
			console.log(step++,'  | storeText | css=.trade_orderbook-market-price | the latest price');
			vars['the latest price'] = await driver.findElement(By.css('.trade_orderbook-market-price')).getText();
			console.log(step++,'  | echo | ${Q}+"and"+${the latest price} | ');
			console.log(vars['Q']+'and'+vars['the latest price'] );
			console.log(step++,'  | executeScript | return ${Q}.split(" ") | R');
			vars['R'] = await driver.executeScript('return arguments[0].split(\'Seller\')', vars['Q']);
			console.log(step++,'  | echo | R | ');
			//console.log(vars["R"])
			let Bwords = words.split('\n');
			console.log(Bwords.indexOf('spread'));
			let sell = Bwords.slice(10,Bwords.indexOf('spread')-1);
			let buy = Bwords.slice(Bwords.indexOf('spread')+2,Bwords.length-1);
			let spread = [Bwords[Bwords.indexOf('spread')-1],Bwords[Bwords.indexOf('spread')+1]];
			console.log(Bwords);
   
			console.log(buy.length);
			console.log(sell.length);
			console.log('sell :'+ sell);
			console.log('buy:'+ buy);
			// let newObject = obj(sell)
    
			console.log(obj(sell));
			console.log(obj(buy));
			console.log(spread);
			let fring = Number(0.01);//eval(spread[1]*0.5)
			let newOrderPriceBuy = (Number(spread[0])+fring).toFixed(2);
			let newOrderPriceSell = (Number(spread[0])+Number(spread[1]-fring)).toFixed(2);
			console.log(newOrderPriceBuy+'\n'+newOrderPriceSell);
			if (fring<spread[1] & obj(sell)[newOrderPriceBuy] == undefined & obj(sell)[newOrderPriceBuy] == undefined){
				console.log('newOrderPriceBuy:'+newOrderPriceBuy);
			}
			if (eval(spread[1]-fring)<spread[1] &obj(sell)[newOrderPriceSell] == undefined & obj(sell)[newOrderPriceSell] == undefined){
				console.log('newOrderPriceSell:'+newOrderPriceSell);
			}

			await driver.sleep(4000);
			console.log(step++,'  | click | css=.text-center:nth-child(2) | ');
			// LIMIT
			await driver.findElement(By.css('.text-center:nth-child(2)')).click();
			console.log(step++,' | click | css=.holla-button-font:nth-child(2) | ');
			// sell
			await driver.findElement(By.css('.holla-button-font:nth-child(2)')).click();
			console.log(step++,' | click | css=.holla-button-font:nth-child(1) | ');
			// buy
			//await driver.findElement(By.css(".holla-button-font:nth-child(1)")).click()
		
	
			console.log(step++,'  | click | name=price | ');
			await driver.findElement(By.name('price')).click();
			await driver.findElement(By.name('price')).clear();
			console.log(step++,'  | type | name=price | newOrderPriceSell');
			await driver.findElement(By.name('price')).sendKeys(newOrderPriceSell);
			console.log(step++,'  | sendKeys | name=price | ${KEY_ENTER}');
			console.log(step++,'  | click | name=size | ');
			await driver.findElement(By.name('size')).click();
			console.log(step++,'  | type | name=size | 1');
			await driver.findElement(By.name('size')).sendKeys('1');
			console.log(step++,'  | click | css=.holla-button | ');

		
			console.log(step++,'  | click | css=.holla-button | ');


			await driver.findElement(By.css('.holla-button')).click();
			console.log(step++,'  | click | css=.text-capitalize | ');
			await driver.findElement(By.css('.text-capitalize')).click();
			console.log(step++,'  | assertText | css=.text-capitalize | Limit Sell');
			assert(await driver.findElement(By.css('.text-capitalize')).getText() == 'Limit Sell');
			console.log(step++,'  | click | css=.notification-content-information > .d-flex:nth-child(2) > .f-1:nth-child(2) | ');
			await driver.findElement(By.css('.notification-content-information > .d-flex:nth-child(2) > .f-1:nth-child(2)')).click();
			console.log(step++,' | assertText | css=.notification-content-information > .d-flex:nth-child(2) > .f-1:nth-child(2) | 1 XHT');
			assert(await driver.findElement(By.css('.notification-content-information > .d-flex:nth-child(2) > .f-1:nth-child(2)')).getText() == '1 XHT');
			console.log(step++,' | click | css=.notification-content-information > .d-flex:nth-child(3) > .f-1:nth-child(2) | ');
			await driver.findElement(By.css('.notification-content-information > .d-flex:nth-child(3) > .f-1:nth-child(2)')).click();
			console.log(step++,' | assertText | css=.notification-content-information > .d-flex:nth-child(3) > .f-1:nth-child(2) | 1 USDT');
			//assert(await driver.findElement(By.css('.notification-content-information > .d-flex:nth-child(3) > .f-1:nth-child(2)')).getText() == newOrderPriceSell.toFixed(1)+' USDT');
			console.log(step++,' | click | css=.d-flex > .holla-button:nth-child(3) | ');
			await driver.sleep(1000);
			await driver.findElement(By.css('.d-flex > .holla-button:nth-child(3)')).click();
		
			util.hollatimestamp();
			console.log('Timestamp : '+String(util.getHollatimestamp()));
			await driver.sleep(3000);
			vars['newPrice'] = await driver.findElement(By.css('.fill-ask:nth-child(1) > .d-flex > .trade_orderbook-cell-price')).getText();
			expect(Number(vars['newPrice'])).to.equal(Number(newOrderPriceSell));
			await driver.sleep(4000);
			console.log(step++,' | click | css=.table_body-row:nth-child(1) .action_notification-text | ');
			//	await driver.findElement(By.css('.table_body-row:nth-child(1) .action_notification-text')).click();

	
			// LIMIT
			await driver.findElement(By.css('.text-center:nth-child(2)')).click();
			console.log(step++,' | click | css=.holla-button-font:nth-child(2) | ');
			// sell
			// await driver.findElement(By.css(".holla-button-font:nth-child(2)")).click()
			console.log(step++,' | click | css=.holla-button-font:nth-child(1) | ');
			// buy
			await driver.findElement(By.css('.holla-button-font:nth-child(1)')).click();
		
	
			console.log(step++,'  | click | name=price | ');
			await driver.findElement(By.name('price')).click();
			await driver.findElement(By.name('price')).clear();
			console.log(step++,'  | type | name=price | newOrderPriceSell');
			await driver.findElement(By.name('price')).sendKeys(newOrderPriceBuy);
			console.log(step++,'  | sendKeys | name=price | ${KEY_ENTER}');
			console.log(step++,'  | click | name=size | ');
			await driver.findElement(By.name('size')).click();
			await driver.findElement(By.name('size')).clear();
			console.log(step++,' | type | name=size | 1');
			await driver.findElement(By.name('size')).sendKeys('1');
			console.log(step++,'  | click | css=.holla-button | ');

		

			await driver.findElement(By.css('.holla-button')).click();
			console.log(step++,'  | click | css=.text-capitalize | ');
			await driver.findElement(By.css('.text-capitalize')).click();
			console.log(step++,'  | assertText | css=.text-capitalize | Limit Sell');
			assert(await driver.findElement(By.css('.text-capitalize')).getText() == 'Limit Buy');
			console.log(step++,'  | click | css=.notification-content-information > .d-flex:nth-child(2) > .f-1:nth-child(2) | ');
			await driver.findElement(By.css('.notification-content-information > .d-flex:nth-child(2) > .f-1:nth-child(2)')).click();
			console.log(step++,'  | assertText | css=.notification-content-information > .d-flex:nth-child(2) > .f-1:nth-child(2) | 1 XHT');
			assert(await driver.findElement(By.css('.notification-content-information > .d-flex:nth-child(2) > .f-1:nth-child(2)')).getText() == '1 XHT');
			console.log(step++,'  | click | css=.notification-content-information > .d-flex:nth-child(3) > .f-1:nth-child(2) | ');
			await driver.findElement(By.css('.notification-content-information > .d-flex:nth-child(3) > .f-1:nth-child(2)')).click();
			console.log(step++,'  | assertText | css=.notification-content-information > .d-flex:nth-child(3) > .f-1:nth-child(2) | 1 USDT');
			//assert(await driver.findElement(By.css('.notification-content-information > .d-flex:nth-child(3) > .f-1:nth-child(2)')).getText() == newOrderPriceBuy.toFixed(1)+' USDT');
			console.log(step++,'  | click | css=.d-flex > .holla-button:nth-child(3) | ');
			await driver.sleep(1000);
			await driver.findElement(By.css('.d-flex > .holla-button:nth-child(3)')).click();
		
			util.hollatimestamp();
			console.log('Timestamp : '+String(util.getHollatimestamp()));
			await driver.sleep(3000);
			vars['newPriceBuy'] = await driver.findElement(By.css('.fill-bid:nth-child(1) > .d-flex > .trade_orderbook-cell-price')).getText();
			expect(Number(vars['newPriceBuy'])).to.equal(Number(newOrderPriceBuy));
			await driver.sleep(4000);
			await driver.findElement(By.css('.table_body-row:nth-child(1) .action_notification-text')).click();
			await driver.sleep(4000);
			await driver.findElement(By.css('.table_body-row:nth-child(1) .action_notification-text')).click();
			console.log('This is the EndOfTest');
		});

	});
}
describe('Main Test', function () {
 
	CancelOrder();
})
module.exports.CancelOrder = CancelOrder;