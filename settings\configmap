##########################################################################################
#### All HollaEx CLI commands rely on ENVIRONMENT_EXCHANGE_NAME below. DO NOT MODIFY IT. ####
#########################################################################################

ENVIRONMENT_EXCHANGE_NAME=hollaex

################################
#### GENERAL CONFIGURATIONS ####
################################

HOLLAEX_CONFIGMAP_API_NAME=my-hollaex-exchange
HOLLAEX_CONFIGMAP_DOMAIN=https://yourdomain.com

HOLLAEX_CONFIGMAP_LOGO_IMAGE=https://bitholla.s3.ap-northeast-2.amazonaws.com/kit/LOGO_IMAGE_LIGHT

ENVIRONMENT_WEB_DEFAULT_COUNTRY=KR
HOLLAEX_CONFIGMAP_EMAILS_TIMEZONE=UTC
HOLLAEX_CONFIGMAP_VALID_LANGUAGES=en
HOLLAEX_CONFIGMAP_NEW_USER_DEFAULT_LANGUAGE=en
ENVIRONMENT_WEB_DEFAULT_LANGUAGE=$HOLLAEX_CONFIGMAP_VALID_LANGUAGES
HOLLAEX_CONFIGMAP_DEFAULT_THEME=dark

HOLLAEX_CONFIGMAP_CURRENCIES=xht,usdt
HOLLAEX_CONFIGMAP_PAIRS='xht-usdt'

HOLLAEX_CONFIGMAP_API_HOST=http://localhost

HOLLAEX_CONFIGMAP_USER_LEVEL_NUMBER=4

HOLLAEX_CONFIGMAP_ADMIN_EMAIL=
HOLLAEX_CONFIGMAP_KYC_EMAIL=
HOLLAEX_CONFIGMAP_SUPPORT_EMAIL=<EMAIL>
HOLLAEX_CONFIGMAP_SUPERVISOR_EMAIL=
HOLLAEX_CONFIGMAP_SENDER_EMAIL=

HOLLAEX_CONFIGMAP_NEW_USER_IS_ACTIVATED=true

HOLLAEX_CONFIGMAP_SMTP_SERVER=
HOLLAEX_CONFIGMAP_SMTP_PORT=587
HOLLAEX_CONFIGMAP_SMTP_USER=

#################################
#### ADVANCED CONFIGURATIONS ####
#################################

HOLLAEX_CONFIGMAP_NODE_ENV=production
HOLLAEX_CONFIGMAP_PORT=10010
HOLLAEX_CONFIGMAP_WEBSOCKET_PORT=10080

HOLLAEX_CONFIGMAP_SEND_EMAIL_TO_SUPPORT=true

HOLLAEX_CONFIGMAP_NETWORK=mainnet

HOLLAEX_CONFIGMAP_NETWORK_URL=

HOLLAEX_CONFIGMAP_VAULT_NAME=

HOLLAEX_CONFIGMAP_DB_SSL=false

HOLLAEX_CONFIGMAP_LOG_LEVEL=verbose

HOLLAEX_CONFIGMAP_KIT_VERSION=$(cat version)

ENVIRONMENT_EXCHANGE_RUN_MODE=api,stream,plugins

ENVIRONMENT_DOCKER_COMPOSE_RUN_POSTGRESQL_DB=true
ENVIRONMENT_DOCKER_COMPOSE_RUN_REDIS=true

ENVIRONMENT_KUBERNETES_RUN_POSTGRESQL_DB=true
ENVIRONMENT_KUBERNETES_POSTGRESQL_DB_VOLUMESIZE=50Gi

ENVIRONMENT_KUBERNETES_RUN_REDIS=true

ENVIRONMENT_KUBERNETES_POSTGRESQL_DB_NODESELECTOR="{}"
ENVIRONMENT_KUBERNETES_REDIS_NODESELECTOR="{}"
ENVIRONMENT_KUBERNETES_EXCHANGE_STATEFUL_NODESELECTOR="{}"
ENVIRONMENT_KUBERNETES_EXCHANGE_STATELESS_NODESELECTOR="{}"

ENVIRONMENT_DOCKER_IMAGE_VERSION=2.2.0

ENVIRONMENT_DOCKER_IMAGE_POSTGRESQL_REGISTRY=postgres
ENVIRONMENT_DOCKER_IMAGE_POSTGRESQL_VERSION=10.9-alpine

ENVIRONMENT_DOCKER_IMAGE_REDIS_REGISTRY=redis
ENVIRONMENT_DOCKER_IMAGE_REDIS_VERSION=6.0.9-alpine

ENVIRONMENT_DOCKER_IMAGE_INFLUXDB_REGISTRY=influxdb
ENVIRONMENT_DOCKER_IMAGE_INFLUXDB_VERSION=1.8.3

ENVIRONMENT_DOCKER_IMAGE_LOCAL_NGINX_REGISTRY=bitholla/nginx-with-certbot
ENVIRONMENT_DOCKER_IMAGE_LOCAL_NGINX_VERSION=1.15.8

ENVIRONMENT_LOCAL_NGINX_HTTP_PORT=80
ENVIRONMENT_LOCAL_NGINX_HTTPS_PORT=443

ENVIRONMENT_KUBERNETES_API_SERVER_REPLICAS=1

ENVIRONMENT_DOCKER_COMPOSE_GENERATE_ENV_ENABLE=true
ENVIRONMENT_DOCKER_COMPOSE_GENERATE_YAML_ENABLE=true
ENVIRONMENT_DOCKER_COMPOSE_GENERATE_NGINX_UPSTREAM=true

ENVIRONMENT_KUBERNETES_GENERATE_CONFIGMAP_ENABLE=true
ENVIRONMENT_KUBERNETES_GENERATE_SECRET_ENABLE=true
ENVIRONMENT_KUBERNETES_GENERATE_INGRESS_ENABLE=true

ENVIRONMENT_KUBERNETES_INGRESS_CERT_MANAGER_ISSUER=

ENVIRONMENT_KUBERNETES_ALLOW_EXTERNAL_POSTGRESQL_DB_ACCESS=false
ENVIRONMENT_KUBERNETES_EXTERNAL_POSTGRESQL_DB_ACCESS_PORT=40000

ENVIRONMENT_KUBERNETES_ALLOW_EXTERNAL_REDIS_ACCESS=false
ENVIRONMENT_KUBERNETES_EXTERNAL_REDIS_ACCESS_PORT=40001

ENVIRONMENT_KUBERNETES_RESTART_NOTIFICATION_WEBHOOK_URL=

ENVIRONMENT_USER_HOLLAEX_CORE_IMAGE_REGISTRY=bitholla/my-hollaex-server
ENVIRONMENT_USER_HOLLAEX_CORE_IMAGE_VERSION=$ENVIRONMENT_DOCKER_IMAGE_VERSION

ENVIRONMENT_USER_HOLLAEX_WEB_IMAGE_REGISTRY=bitholla/my-hollaex-web
ENVIRONMENT_USER_HOLLAEX_WEB_IMAGE_VERSION=latest

ENVIRONMENT_API_CPU_LIMITS=0.1
ENVIRONMENT_API_MEMORY_LIMITS=512Mi
ENVIRONMENT_API_CPU_REQUESTS=0.05
ENVIRONMENT_API_MEMORY_REQUESTS=512Mi

ENVIRONMENT_STREAM_CPU_LIMITS=0.1
ENVIRONMENT_STREAM_MEMORY_LIMITS=256Mi
ENVIRONMENT_STREAM_CPU_REQUESTS=0.05
ENVIRONMENT_STREAM_MEMORY_REQUESTS=256Mi

ENVIRONMENT_PLUGINS_CPU_LIMITS=0.1
ENVIRONMENT_PLUGINS_MEMORY_LIMITS=512Mi
ENVIRONMENT_PLUGINS_CPU_REQUESTS=0.05
ENVIRONMENT_PLUGINS_MEMORY_REQUESTS=256Mi

ENVIRONMENT_POSTGRESQL_CPU_LIMITS=0.1
ENVIRONMENT_POSTGRESQL_MEMORY_LIMITS=100Mi
ENVIRONMENT_POSTGRESQL_CPU_REQUESTS=0.1
ENVIRONMENT_POSTGRESQL_MEMORY_REQUESTS=100Mi

ENVIRONMENT_REDIS_CPU_LIMITS=0.1
ENVIRONMENT_REDIS_MEMORY_LIMITS=100Mi
ENVIRONMENT_REDIS_CPU_REQUESTS=0.1
ENVIRONMENT_REDIS_MEMORY_REQUESTS=100Mi

ENVIRONMENT_WEB_CPU_LIMITS=0.05
ENVIRONMENT_WEB_MEMORY_LIMITS=128Mi
ENVIRONMENT_WEB_CPU_REQUESTS=0.01
ENVIRONMENT_WEB_MEMORY_REQUESTS=128Mi

ENVIRONMENT_KUBERNETES_S3_BACKUP_CRONJOB_RULE='0 1 * * *'
ENVIRONMENT_KUBERNETES_S3_BACKUP_CRONJOB_REGION=
ENVIRONMENT_KUBERNETES_S3_BACKUP_CRONJOB_BUCKET=
ENVIRONMENT_KUBERNETES_S3_BACKUP_CRONJOB_TIMEZONE=UTC

ENVIRONMENT_KUBERNETES_STREAM_SERVER_REPLICAS=1
ENVIRONMENT_KUBERNETES_WEB_SERVER_REPLICAS=1

ENVIRONMENT_KUBERNETES_API_HPA_ENABLE=false
ENVIRONMENT_KUBERNETES_STREAM_HPA_ENABLE=false
ENVIRONMENT_KUBERNETES_WEB_HPA_ENABLE=false
ENVIRONMENT_KUBERNETES_API_HPA_AVGMEMORY=1300000000
ENVIRONMENT_KUBERNETES_STREAM_HPA_AVGMEMORY=300000000
ENVIRONMENT_KUBERNETES_WEB_HPA_AVGMEMORY=500000000

ENVIRONMENT_KUBERNETES_API_HPA_MAXREPLICAS=4
ENVIRONMENT_KUBERNETES_STREAM_HPA_MAXREPLICAS=4
ENVIRONMENT_KUBERNETES_WEB_HPA_MAXREPLICAS=4
