{"version": "2.0.0", "lockfileVersion": 1, "requires": true, "dependencies": {"@babel/code-frame": {"version": "7.10.4", "resolved": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.10.4.tgz", "integrity": "sha512-vG6SvB6oYEhvgisZNFRmRCUkLz11c7rp+tbNTynGqc6mS1d5ATd/sGyV6W0KZZnXRKMTzZDRgQT3Ou9jhpAfUg==", "requires": {"@babel/highlight": "^7.10.4"}}, "@babel/helper-validator-identifier": {"version": "7.10.4", "resolved": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.10.4.tgz", "integrity": "sha512-3U9y+43hz7ZM+rzG24Qe2mufW5KhvFg/NhnNph+i9mgCtdTCtMJuI1TMkrIUiK7Ix4PYlRF9I5dhqaLYA/ADXw=="}, "@babel/highlight": {"version": "7.10.4", "resolved": "https://registry.npmjs.org/@babel/highlight/-/highlight-7.10.4.tgz", "integrity": "sha512-i6rgnR/YgPEQzZZnbTHHuZdlE8qyoBNalD6F+q4vAFlcMEcqmkoG+mPqJYJCo63qPf74+Y1UZsl3l6f7/RIkmA==", "requires": {"@babel/helper-validator-identifier": "^7.10.4", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}}, "@sailshq/lodash": {"version": "3.10.4", "resolved": "https://registry.npmjs.org/@sailshq/lodash/-/lodash-3.10.4.tgz", "integrity": "sha512-YXJqp9gdHcZKAmBY/WnwFpPtNQp2huD/ME2YMurH2YHJvxrVzYsmpKw/pb7yINArRpp8E++fwbQd3ajYXGA45Q=="}, "@types/geojson": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/@types/geojson/-/geojson-1.0.6.tgz", "integrity": "sha512-Xqg/lIZMrUd0VRmSRbCAewtwGZiAk3mEUDvV4op1tGl+LvyPcb/MIOSxTl9z+9+J+R4/vpjiCAT4xeKzH9ji1w=="}, "@types/node": {"version": "14.6.2", "resolved": "https://registry.npmjs.org/@types/node/-/node-14.6.2.tgz", "integrity": "sha512-onlIwbaeqvZyniGPfdw/TEhKIh79pz66L1q06WUQqJLnAb6wbjvOtepLYTGHTqzdXgBYIE3ZdmqHDGsRsbBz7A=="}, "@types/normalize-package-data": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@types/normalize-package-data/-/normalize-package-data-2.4.0.tgz", "integrity": "sha512-f5j5b/Gf71L+dbqxIpQ4Z2WlmI/mPJ0fOkGGmFgtb6sAu97EPczzbS3/tJKxmcYDj55OX6ssqwDAWOHIYDRDGA=="}, "accepts": {"version": "1.3.7", "resolved": "https://registry.npmjs.org/accepts/-/accepts-1.3.7.tgz", "integrity": "sha512-Il80Qs2WjYlJIBNzNkK6KYqlVMTbZLXgHx2oT0pU/fjRHyEp+PEfEPY0R3WCwAGVOtauxh1hOxNgIf5bv7dQpA==", "requires": {"mime-types": "~2.1.24", "negotiator": "0.6.2"}}, "after": {"version": "0.8.2", "resolved": "https://registry.npmjs.org/after/-/after-0.8.2.tgz", "integrity": "sha1-/ts5T58OAqqXaOcCvaI7UF+ufh8="}, "after-all-results": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/after-all-results/-/after-all-results-2.0.0.tgz", "integrity": "sha1-asL8ICtQD4jaj09VMM+hAPTGotA="}, "ajv": {"version": "6.12.5", "resolved": "https://registry.npmjs.org/ajv/-/ajv-6.12.5.tgz", "integrity": "sha512-lRF8RORchjpKG50/WFf8xmg7sgCLFiYNNnqdKflk63whMQcWR5ngGjiSXkL9bjxy6B2npOK2HSMN49jEBMSkag==", "requires": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "amp": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/amp/-/amp-0.3.1.tgz", "integrity": "sha1-at+NWKdPNh6CwfqNOJwHnhOfxH0="}, "amp-message": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/amp-message/-/amp-message-0.1.2.tgz", "integrity": "sha1-p48cmJlQh602GSpBKY5NtJ49/EU=", "requires": {"amp": "0.3.1"}}, "ansi-regex": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8="}, "ansi-styles": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz", "integrity": "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==", "requires": {"color-convert": "^1.9.0"}}, "anymatch": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/anymatch/-/anymatch-2.0.0.tgz", "integrity": "sha512-5teOsQWABXHHBFP9y3skS5P3d/WfWXpv3FUpy+LorMrNYaT9pI4oLMQX7jzQ2KklNpGpWHzdCXTDT2Y3XGlZBw==", "requires": {"micromatch": "^3.1.4", "normalize-path": "^2.1.1"}, "dependencies": {"normalize-path": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-2.1.1.tgz", "integrity": "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=", "requires": {"remove-trailing-separator": "^1.0.1"}}}}, "append-field": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/append-field/-/append-field-1.0.0.tgz", "integrity": "sha1-HjRA6RXwsSA9I3SOeO3XubW0PlY="}, "argparse": {"version": "1.0.10", "resolved": "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz", "integrity": "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==", "requires": {"sprintf-js": "~1.0.2"}, "dependencies": {"sprintf-js": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz", "integrity": "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw="}}}, "arr-diff": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/arr-diff/-/arr-diff-4.0.0.tgz", "integrity": "sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA="}, "arr-flatten": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/arr-flatten/-/arr-flatten-1.1.0.tgz", "integrity": "sha512-L3hKV5R/p5o81R7O02IGnwpDmkp6E982XhtbuwSe3O4qOtMMMtodicASA1Cny2U+aCXcNpml+m4dPsvsJ3jatg=="}, "arr-union": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/arr-union/-/arr-union-3.1.0.tgz", "integrity": "sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ="}, "array-flatten": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz", "integrity": "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI="}, "array-unique": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/array-unique/-/array-unique-0.3.2.tgz", "integrity": "sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg="}, "arraybuffer.slice": {"version": "0.0.7", "resolved": "https://registry.npmjs.org/arraybuffer.slice/-/arraybuffer.slice-0.0.7.tgz", "integrity": "sha512-wGUIVQXuehL5TCqQun8OW81jGzAWycqzFF8lFp+GOM5BXLYj3bKNsYC4daB7n6XjCqxQA/qgTJ+8ANR3acjrog=="}, "asn1": {"version": "0.2.4", "resolved": "https://registry.npmjs.org/asn1/-/asn1-0.2.4.tgz", "integrity": "sha512-jxwzQpLQjSmWXgwaCZE9Nz+glAG01yF1QnWgbhGwHI5A6FRIEY6IVqtHhIepHqI7/kyEyQEagBC5mBEFlIYvdg==", "requires": {"safer-buffer": "~2.1.0"}}, "assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU="}, "assertion-error": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/assertion-error/-/assertion-error-1.1.0.tgz", "integrity": "sha512-jgsaNduz+ndvGyFt3uSuWqvy4lCnIJiovtouQN5JZHOKCS2QuhEdbcQHFhVksz2N2U9hXJo8odG7ETyWlEeuDw=="}, "assign-symbols": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assign-symbols/-/assign-symbols-1.0.0.tgz", "integrity": "sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c="}, "async": {"version": "2.6.3", "resolved": "https://registry.npmjs.org/async/-/async-2.6.3.tgz", "integrity": "sha512-zflvls11DCy+dQWzTW2dzuilv8Z5X/pjfmZOWba6TNIVDm+2UDaJmXSOXlasHKfNBs8oo3M0aT50fDEWfKZjXg==", "requires": {"lodash": "^4.17.14"}, "dependencies": {"lodash": {"version": "4.17.20", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.20.tgz", "integrity": "sha512-PlhdFcillOINfeV7Ni6oF1TAEayyZBoZ8bcshTHqOYJYlrqzRK5hagpagky5o4HfCzzd1TRkXPMFq6cKk9rGmA=="}}}, "async-cache": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/async-cache/-/async-cache-1.1.0.tgz", "integrity": "sha1-SppaidBl7F2OUlS9nulrp2xTK1o=", "requires": {"lru-cache": "^4.0.0"}}, "async-each": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/async-each/-/async-each-1.0.3.tgz", "integrity": "sha512-z/WhQ5FPySLdvREByI2vZiTWwCnF0moMJ1hK9YQwDTHKh6I7/uSckMetoRGb5UBZPC1z0jlw+n/XCgjeH7y1AQ=="}, "async-limiter": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/async-limiter/-/async-limiter-1.0.1.tgz", "integrity": "sha512-csOlWGAcRFJaI6m+F2WKdnMKr4HhdhFVBk0H/QbJFMCr+uO2kwohwXQPxw/9OCxp05r5ghVBFSyioixx3gfkNQ=="}, "async-listener": {"version": "0.6.10", "resolved": "https://registry.npmjs.org/async-listener/-/async-listener-0.6.10.tgz", "integrity": "sha512-gpuo6xOyF4D5DE5WvyqZdPA3NGhiT6Qf07l7DCB0wwDEsLvDIbCr6j9S5aj5Ch96dLace5tXVzWBZkxU/c5ohw==", "requires": {"semver": "^5.3.0", "shimmer": "^1.1.0"}, "dependencies": {"semver": {"version": "5.7.1", "resolved": "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz", "integrity": "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ=="}}}, "async-value": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/async-value/-/async-value-1.2.2.tgz", "integrity": "sha1-hFF6Hny2saW14YH6Mb4QQ3t/sSU="}, "async-value-promise": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/async-value-promise/-/async-value-promise-1.1.1.tgz", "integrity": "sha512-c2RFDKjJle1rHa0YxN9Ysu97/QBu3Wa+NOejJxsX+1qVDJrkD3JL/GN1B3gaILAEXJXbu/4Z1lcoCHFESe/APA==", "requires": {"async-value": "^1.2.2"}}, "asynckit": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k="}, "atob": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/atob/-/atob-2.1.2.tgz", "integrity": "sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg=="}, "await-event": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/await-event/-/await-event-2.1.0.tgz", "integrity": "sha1-eOn5JoS65AIvn6C18xShFVD5qnY="}, "awesome-phonenumber": {"version": "2.2.5", "resolved": "https://registry.npmjs.org/awesome-phonenumber/-/awesome-phonenumber-2.2.5.tgz", "integrity": "sha512-CHVEWEtXhaUB4ZGki/MQOlxK9wX+x3rw0/OARxB/srJTBP7b8nZjuE69WKQIUmciYdYGP2qywFPyubewJnFLbQ=="}, "aws-sdk": {"version": "2.205.0", "resolved": "https://registry.npmjs.org/aws-sdk/-/aws-sdk-2.205.0.tgz", "integrity": "sha1-GpNzAlPivgJ6S9OvkkjL2gVz3oA=", "requires": {"buffer": "4.9.1", "events": "^1.1.1", "jmespath": "0.15.0", "querystring": "0.2.0", "sax": "1.2.1", "url": "0.10.3", "uuid": "3.1.0", "xml2js": "0.4.17", "xmlbuilder": "4.2.1"}, "dependencies": {"uuid": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/uuid/-/uuid-3.1.0.tgz", "integrity": "sha512-DIWtzUkw04M4k3bf1IcpS2tngXEL26YUD2M0tMDUpnUrz2hgzUBlD55a4FjdLGPvfHxS6uluGWvaVEqgBcVa+g=="}}}, "aws-sign2": {"version": "0.7.0", "resolved": "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.7.0.tgz", "integrity": "sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg="}, "aws4": {"version": "1.10.1", "resolved": "https://registry.npmjs.org/aws4/-/aws4-1.10.1.tgz", "integrity": "sha512-zg7Hz2k5lI8kb7U32998pRRFin7zJlkfezGJjUc2heaD4Pw2wObakCDVzkKztTm/Ln7eiVvYsjqak0Ed4LkMDA=="}, "backo2": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/backo2/-/backo2-1.0.2.tgz", "integrity": "sha1-MasayLEpNjRj41s+u2n038+6eUc="}, "bagpipes": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/bagpipes/-/bagpipes-0.0.6.tgz", "integrity": "sha1-CWvRDCBi65RYVvkZtxApRVeOR/0=", "requires": {"async": "^1.4.2", "debug": "^2.1.2", "jspath": "^0.3.1", "lodash": "^3.5.0", "machinepack-http": "^2.3.0", "mustache": "^2.1.3", "pipeworks": "^1.3.0"}, "dependencies": {"async": {"version": "1.5.2", "resolved": "https://registry.npmjs.org/async/-/async-1.5.2.tgz", "integrity": "sha1-7GphrlZIDAw8skHJVhjiCJL5Zyo="}, "debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "lodash": {"version": "3.10.1", "resolved": "https://registry.npmjs.org/lodash/-/lodash-3.10.1.tgz", "integrity": "sha1-W/Rejkm6QYnhfUgnid/RW9FAt7Y="}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "balanced-match": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.0.tgz", "integrity": "sha1-ibTRmasr7kneFk6gK4nORi1xt2c="}, "base": {"version": "0.11.2", "resolved": "https://registry.npmjs.org/base/-/base-0.11.2.tgz", "integrity": "sha512-5T6P4xPgpp0YDFvSWwEZ4NoE3aM4QBQXDzmVbraCkFj8zHM+mba8SyqB5DbZWyR7mYHo6Y7BdQo3MoA4m0TeQg==", "requires": {"cache-base": "^1.0.1", "class-utils": "^0.3.5", "component-emitter": "^1.2.1", "define-property": "^1.0.0", "isobject": "^3.0.1", "mixin-deep": "^1.2.0", "pascalcase": "^0.1.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/define-property/-/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "requires": {"is-descriptor": "^1.0.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ==", "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ==", "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg==", "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "base64-arraybuffer": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/base64-arraybuffer/-/base64-arraybuffer-0.1.5.tgz", "integrity": "sha1-c5JncZI7Whl0etZmqlzUv5xunOg="}, "base64-js": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/base64-js/-/base64-js-1.3.1.tgz", "integrity": "sha512-mLQ4i2QO1ytvGWFWmcngKO//JXAQueZvwEKtjgQFM4jIK0kU+ytMfplL8j+n5mspOfjHwoAg+9yhb7BwAHm36g=="}, "base64id": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/base64id/-/base64id-1.0.0.tgz", "integrity": "sha1-R2iMuZu2gE8OBtPnY7HDLlfY5rY="}, "basic-auth": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/basic-auth/-/basic-auth-2.0.1.tgz", "integrity": "sha512-NF+epuEdnUYVlGuhaxbbq+dvJttwLnGY+YixlXlME5KpQ5W3CnXA5cVTneY3SPbPDRkcjMbifrwmFYcClgOZeg==", "requires": {"safe-buffer": "5.1.2"}}, "bcrypt-pbkdf": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz", "integrity": "sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=", "requires": {"tweetnacl": "^0.14.3"}}, "bcryptjs": {"version": "2.4.3", "resolved": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-2.4.3.tgz", "integrity": "sha1-mrVie5PmBiH/fNrF2pczAn3x0Ms="}, "better-assert": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/better-assert/-/better-assert-1.0.2.tgz", "integrity": "sha1-QIZrnhueC1W0gYlDEeaPr/rrxSI=", "requires": {"callsite": "1.0.0"}}, "binary-extensions": {"version": "1.13.1", "resolved": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.13.1.tgz", "integrity": "sha512-Un7MIEDdUC5gNpcGDV97op1Ywk748MpHcFTHoYs6qnj1Z3j7I53VG3nwZhKzoBZmbdRNnb6WRdFlwl7tSDuZGw=="}, "binary-search": {"version": "1.3.6", "resolved": "https://registry.npmjs.org/binary-search/-/binary-search-1.3.6.tgz", "integrity": "sha512-nbE1WxOTTrUWIfsfZ4aHGYu5DOuNkbxGokjV6Z2kxfJK3uaAb8zNK1muzOeipoLHZjInT4Br88BHpzevc681xA=="}, "bindings": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/bindings/-/bindings-1.5.0.tgz", "integrity": "sha512-p2q/t/mhvuOj/UeLlV6566GD/guowlr0hHxClI0W9m7MWYkL1F0hLo+0Aexs9HSPCtR1SXQ0TD3MMKrXZajbiQ==", "optional": true, "requires": {"file-uri-to-path": "1.0.0"}}, "blessed": {"version": "0.1.81", "resolved": "https://registry.npmjs.org/blessed/-/blessed-0.1.81.tgz", "integrity": "sha1-+WLWh+wsNpVwrnGvhDJW5tDKESk="}, "blob": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/blob/-/blob-0.0.5.tgz", "integrity": "sha512-gaqbzQPqOoamawKg0LGVd7SzLgXS+JH61oWprSLH+P+abTczqJbhTR8CmJ2u9/bUYNmHTGJx/UEmn6doAvvuig=="}, "bluebird": {"version": "3.5.3", "resolved": "https://registry.npmjs.org/bluebird/-/bluebird-3.5.3.tgz", "integrity": "sha512-/qKPUQlaW1OyR51WeCPBvRnAlnZFUJkCSG5HzGnuIqhgyJtF+T94lFnn33eiazjRm2LAHVy2guNnaq48X9SJuw=="}, "body-parser": {"version": "1.18.2", "resolved": "https://registry.npmjs.org/body-parser/-/body-parser-1.18.2.tgz", "integrity": "sha1-h2eKGdhLR9hZuDGZvVm84iKxBFQ=", "requires": {"bytes": "3.0.0", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "~1.1.1", "http-errors": "~1.6.2", "iconv-lite": "0.4.19", "on-finished": "~2.3.0", "qs": "6.5.1", "raw-body": "2.3.2", "type-is": "~1.6.15"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "boom": {"version": "4.3.1", "resolved": "https://registry.npmjs.org/boom/-/boom-4.3.1.tgz", "integrity": "sha1-T4owBctKfjiJ90kDD9JbluAdLjE=", "requires": {"hoek": "4.x.x"}}, "brace-expansion": {"version": "1.1.11", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==", "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "braces": {"version": "2.3.2", "resolved": "https://registry.npmjs.org/braces/-/braces-2.3.2.tgz", "integrity": "sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w==", "requires": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}}}, "breadth-filter": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/breadth-filter/-/breadth-filter-2.0.0.tgz", "integrity": "sha512-thQShDXnFWSk2oVBixRCyrWsFoV5tfOpWKHmxwafHQDNxCfDBk539utpvytNjmlFrTMqz41poLwJvA1MW3z0MQ==", "requires": {"object.entries": "^1.0.4"}}, "browser-stdout": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/browser-stdout/-/browser-stdout-1.3.0.tgz", "integrity": "sha1-81HTKWnTL6XXpVZxVCY9korjvR8=", "dev": true}, "buffer": {"version": "4.9.1", "resolved": "https://registry.npmjs.org/buffer/-/buffer-4.9.1.tgz", "integrity": "sha1-bRu2AbB6TvztlwlBMgkwJ8lbwpg=", "requires": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}}, "buffer-crc32": {"version": "0.2.13", "resolved": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.13.tgz", "integrity": "sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI="}, "buffer-equal-constant-time": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/buffer-equal-constant-time/-/buffer-equal-constant-time-1.0.1.tgz", "integrity": "sha1-+OcRMvf/5uAaXJaXpMbz5I1cyBk="}, "buffer-from": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.1.tgz", "integrity": "sha512-MQcXEUbCKtEo7bhqEs6560Hyd4XaovZlO/k9V3hjVUF/zwW7KBVdSK4gIt/bzwS9MbR5qob+F5jusZsb0YQK2A=="}, "buffer-writer": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/buffer-writer/-/buffer-writer-1.0.1.tgz", "integrity": "sha1-Iqk2kB4wKa/NdUfrRIfOtpejvwg="}, "busboy": {"version": "0.2.14", "resolved": "https://registry.npmjs.org/busboy/-/busboy-0.2.14.tgz", "integrity": "sha1-bCpiLvz0fFe7vh4qnDetNseSVFM=", "requires": {"dicer": "0.2.5", "readable-stream": "1.1.x"}, "dependencies": {"isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="}, "readable-stream": {"version": "1.1.14", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.1.14.tgz", "integrity": "sha1-fPTFTvZI44EwhMY23SB54WbAgdk=", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ="}}}, "bytes": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/bytes/-/bytes-3.0.0.tgz", "integrity": "sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg="}, "cache-base": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/cache-base/-/cache-base-1.0.1.tgz", "integrity": "sha512-AKcdTnFSWATd5/GCPRxr2ChwIJ85CeyrEyjRHlKxQ56d4XJMGym0uAiKn0xbLOGOl3+yRpOTi484dVCEc5AUzQ==", "requires": {"collection-visit": "^1.0.0", "component-emitter": "^1.2.1", "get-value": "^2.0.6", "has-value": "^1.0.0", "isobject": "^3.0.1", "set-value": "^2.0.0", "to-object-path": "^0.3.0", "union-value": "^1.0.0", "unset-value": "^1.0.0"}}, "callsite": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/callsite/-/callsite-1.0.0.tgz", "integrity": "sha1-KAOY5dZkvXQDi28JBRU+borxvCA="}, "camelize": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/camelize/-/camelize-1.0.0.tgz", "integrity": "sha1-FkpUg+Yw+kMh5a8HAg5TGDGyYJs="}, "caseless": {"version": "0.12.0", "resolved": "https://registry.npmjs.org/caseless/-/caseless-0.12.0.tgz", "integrity": "sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw="}, "chai": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/chai/-/chai-4.2.0.tgz", "integrity": "sha512-XQU3bhBukrOsQCuwZndwGcCVQHyZi53fQ6Ys1Fym7E4olpIqqZZhhoFJoaKVvV17lWQoXYwgWN2nF5crA8J2jw==", "requires": {"assertion-error": "^1.1.0", "check-error": "^1.0.2", "deep-eql": "^3.0.1", "get-func-name": "^2.0.0", "pathval": "^1.1.0", "type-detect": "^4.0.5"}}, "chalk": {"version": "2.4.2", "resolved": "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz", "integrity": "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==", "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "charm": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/charm/-/charm-0.1.2.tgz", "integrity": "sha1-BsIe7RobBq62dVPNxT4jJ0usIpY="}, "check-error": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/check-error/-/check-error-1.0.2.tgz", "integrity": "sha1-V00xLt2Iu13YkS6Sht1sCu1KrII="}, "chokidar": {"version": "2.1.8", "resolved": "https://registry.npmjs.org/chokidar/-/chokidar-2.1.8.tgz", "integrity": "sha512-ZmZUazfOzf0Nve7duiCKD23PFSCs4JPoYyccjUFF3aQkQadqBhfzhjkwBH2mNOG9cTBwhamM37EIsIkZw3nRgg==", "requires": {"anymatch": "^2.0.0", "async-each": "^1.0.1", "braces": "^2.3.2", "fsevents": "^1.2.7", "glob-parent": "^3.1.0", "inherits": "^2.0.3", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.2.1", "upath": "^1.1.1"}}, "class-utils": {"version": "0.3.6", "resolved": "https://registry.npmjs.org/class-utils/-/class-utils-0.3.6.tgz", "integrity": "sha512-qOhPa/Fj7s6TY8H8esGu5QNpMMQxz79h+urzrNYN6mn+9BnxlDGf5QZ+XeCDsxSjPqsSR56XOZOJmpeurnLMeg==", "requires": {"arr-union": "^3.1.0", "define-property": "^0.2.5", "isobject": "^3.0.0", "static-extend": "^0.1.1"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}}}, "cli-table-redemption": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/cli-table-redemption/-/cli-table-redemption-1.0.1.tgz", "integrity": "sha512-SjVCciRyx01I4azo2K2rcc0NP/wOceXGzG1ZpYkEulbbIxDA/5YWv0oxG2HtQ4v8zPC6bgbRI7SbNaTZCxMNkg==", "requires": {"chalk": "^1.1.3"}, "dependencies": {"ansi-styles": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-2.2.1.tgz", "integrity": "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4="}, "chalk": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/chalk/-/chalk-1.1.3.tgz", "integrity": "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=", "requires": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}}, "supports-color": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-2.0.0.tgz", "integrity": "sha1-U10EXOa2Nj+kARcIRimZXp3zJMc="}}}, "cls-bluebird": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/cls-bluebird/-/cls-bluebird-2.1.0.tgz", "integrity": "sha1-N+8eCAqP+1XC9BZPU28ZGeeWiu4=", "requires": {"is-bluebird": "^1.0.2", "shimmer": "^1.1.0"}}, "co": {"version": "4.6.0", "resolved": "https://registry.npmjs.org/co/-/co-4.6.0.tgz", "integrity": "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ="}, "collection-visit": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/collection-visit/-/collection-visit-1.0.0.tgz", "integrity": "sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=", "requires": {"map-visit": "^1.0.0", "object-visit": "^1.0.0"}}, "color": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/color/-/color-3.0.0.tgz", "integrity": "sha512-jCpd5+s0s0t7p3pHQKpnJ0TpQKKdleP71LWcA0aqiljpiuAkOSUFN/dyH8ZwF0hRmFlrIuRhufds1QyEP9EB+w==", "requires": {"color-convert": "^1.9.1", "color-string": "^1.5.2"}}, "color-convert": {"version": "1.9.3", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz", "integrity": "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==", "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="}, "color-string": {"version": "1.5.3", "resolved": "https://registry.npmjs.org/color-string/-/color-string-1.5.3.tgz", "integrity": "sha512-dC2C5qeWoYkxki5UAXapdjqO672AM4vZuPGRQfO8b5HKuKGBbKWpITyDYN7TOFKvRW7kOgAn3746clDBMDJyQw==", "requires": {"color-name": "^1.0.0", "simple-swizzle": "^0.2.2"}}, "colornames": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/colornames/-/colornames-1.1.1.tgz", "integrity": "sha1-+IiQMGhcfE/54qVZ9Qd+t2qBb5Y="}, "colors": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/colors/-/colors-1.4.0.tgz", "integrity": "sha512-a+UqTh4kgZg/SlGvfbzDHpgRu7AAQOmmqRHJnxhRZICKFUT91brVhNNt58CMWU9PsBbv3PDCZUHbVxuDiH2mtA=="}, "colorspace": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/colorspace/-/colorspace-1.1.2.tgz", "integrity": "sha512-vt+OoIP2d76xLhjwbBaucYlNSpPsrJWPlBTtwCpQKIu6/CSMutyzX93O/Do0qzpH3YoHEes8YEFXyZ797rEhzQ==", "requires": {"color": "3.0.x", "text-hex": "1.0.x"}}, "combined-stream": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "requires": {"delayed-stream": "~1.0.0"}}, "commander": {"version": "2.20.3", "resolved": "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz", "integrity": "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ=="}, "complex.js": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/complex.js/-/complex.js-2.0.4.tgz", "integrity": "sha512-Syl95HpxUTS0QjwNxencZsKukgh1zdS9uXeXX2Us0pHaqBR6kiZZi0AkZ9VpZFwHJyVIUVzI4EumjWdXP3fy6w=="}, "component-bind": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/component-bind/-/component-bind-1.0.0.tgz", "integrity": "sha1-AMYIq33Nk4l8AAllGx06jh5zu9E="}, "component-emitter": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/component-emitter/-/component-emitter-1.2.1.tgz", "integrity": "sha1-E3kY1teCg/ffemt8WmPhQOaUJeY="}, "component-inherit": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/component-inherit/-/component-inherit-0.0.3.tgz", "integrity": "sha1-ZF/ErfWLcrZJ1crmUTVhnbJv8UM="}, "concat-map": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="}, "concat-stream": {"version": "1.6.2", "resolved": "https://registry.npmjs.org/concat-stream/-/concat-stream-1.6.2.tgz", "integrity": "sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw==", "requires": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}, "dependencies": {"readable-stream": {"version": "2.3.7", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.7.tgz", "integrity": "sha512-<PERSON>bho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw==", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==", "requires": {"safe-buffer": "~5.1.0"}}}}, "config": {"version": "1.31.0", "resolved": "https://registry.npmjs.org/config/-/config-1.31.0.tgz", "integrity": "sha512-Ep/l9Rd1J9IPueztJfpbOqVzuKHQh4ZODMNt9xqTYdBBNRXbV4oTu34kCkkfdRVcDq0ohtpaeXGgb+c0LQxFRA==", "requires": {"json5": "^1.0.1"}}, "console-log-level": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/console-log-level/-/console-log-level-1.4.1.tgz", "integrity": "sha512-VZzbIORbP+PPcN/gg3DXClTLPLg5Slwd5fL2MIc+o1qZ4BXBvWyc6QxPk6T/Mkr6IVjRpoAGf32XxP3ZWMVRcQ=="}, "container-info": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/container-info/-/container-info-1.0.1.tgz", "integrity": "sha512-wk/+uJvPHOFG+JSwQS+fw6H6yw3Oyc8Kw9L4O2MN817uA90OqJ59nlZbbLPqDudsjJ7Tetee3pwExdKpd2ahjQ=="}, "content-disposition": {"version": "0.5.2", "resolved": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.2.tgz", "integrity": "sha1-DPaLud318r55YcOoUXjLhdunjLQ="}, "content-security-policy-builder": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/content-security-policy-builder/-/content-security-policy-builder-2.0.0.tgz", "integrity": "sha512-j+Nhmj1yfZAikJLImCvPJFE29x/UuBi+/MWqggGGc515JKaZrjuei2RhULJmy0MsstW3E3htl002bwmBNMKr7w=="}, "content-type": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/content-type/-/content-type-1.0.4.tgz", "integrity": "sha512-hIP3EEPs8tB9AT1L+NUqtwOAps4mk2Zob89MWXMHjHWg9milF/j4osnnQLXBCBFBk/tvIG/tUc9mOUJiPBhPXA=="}, "continuation-local-storage": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/continuation-local-storage/-/continuation-local-storage-3.2.1.tgz", "integrity": "sha512-jx44cconVqkCEEyLSKWwkvUXwO561jXMa3LPjTPsm5QR22PA0/mhe33FT4Xb5y74JDvt/Cq+5lm8S8rskLv9ZA==", "requires": {"async-listener": "^0.6.0", "emitter-listener": "^1.1.1"}}, "convert-to-ecmascript-compatible-varname": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/convert-to-ecmascript-compatible-varname/-/convert-to-ecmascript-compatible-varname-0.1.5.tgz", "integrity": "sha1-9npJOMUjNENWQlBHnGcBS6yHhJk="}, "cookie": {"version": "0.4.1", "resolved": "https://registry.npmjs.org/cookie/-/cookie-0.4.1.tgz", "integrity": "sha512-ZwrFkGJxUR3EIoXtO+yVE69Eb7KlixbaeAWfBQB9vVsNn/o+Yw69gBWSSDK825hQNdN+wF8zELf3dFNl/kxkUA=="}, "cookie-signature": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz", "integrity": "sha1-4wOogrNCzD7oylE6eZmXNNqzriw="}, "cookiejar": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/cookiejar/-/cookiejar-2.1.2.tgz", "integrity": "sha512-Mw+adcfzPxcPeI+0WlvRrr/3lGVO0bD75SxX6811cxSh1Wbxx7xZBGK1eVtDf6si8rg2lhnUjsVLMFMfbRIuwA=="}, "copy-descriptor": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/copy-descriptor/-/copy-descriptor-0.1.1.tgz", "integrity": "sha1-Z29us8OZl8LuGsOpJP1hJHSPV40="}, "core-js": {"version": "2.6.11", "resolved": "https://registry.npmjs.org/core-js/-/core-js-2.6.11.tgz", "integrity": "sha512-5wjnpaT/3dV+XB4borEsnAYQchn00XSgTAWKDkEqv+K8KevjbzmofK6hfJ9TZIlpj2N0xQpazy7PiRQiWHqzWg=="}, "core-util-is": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.2.tgz", "integrity": "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac="}, "cors": {"version": "2.8.5", "resolved": "https://registry.npmjs.org/cors/-/cors-2.8.5.tgz", "integrity": "sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==", "requires": {"object-assign": "^4", "vary": "^1"}}, "cron": {"version": "1.8.2", "resolved": "https://registry.npmjs.org/cron/-/cron-1.8.2.tgz", "integrity": "sha512-Gk2c4y6xKEO8FSAUTklqtfSr7oTq0CiPQeLBG5Fl0qoXpZyMcj1SG59YL+hqq04bu6/IuEA7lMkYDAplQNKkyg==", "requires": {"moment-timezone": "^0.5.x"}}, "cryptiles": {"version": "3.1.4", "resolved": "https://registry.npmjs.org/cryptiles/-/cryptiles-3.1.4.tgz", "integrity": "sha512-8I1sgZHfVwcSOY6mSGpVU3lw/GSIZvusg8dD2+OGehCJpOhQRLNcH0qb9upQnOH4XhgxxFJSg6E2kx95deb1Tw==", "requires": {"boom": "5.x.x"}, "dependencies": {"boom": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/boom/-/boom-5.2.0.tgz", "integrity": "sha512-Z5BTk6ZRe4tXXQlkqftmsAUANpXmuwlsF5Oov8ThoMbQRzdGTA1ngYRW160GexgOgjsFOKJz0LYhoNi+2AMBUw==", "requires": {"hoek": "4.x.x"}}}}, "cycle": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/cycle/-/cycle-1.0.3.tgz", "integrity": "sha1-IegLK+hYD5i0aPN5QwZisEbDStI="}, "dashdash": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/dashdash/-/dashdash-1.14.1.tgz", "integrity": "sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=", "requires": {"assert-plus": "^1.0.0"}}, "dasherize": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/dasherize/-/dasherize-2.0.0.tgz", "integrity": "sha1-bYCcnNDPe7iVLYD8hPoT1H3bEwg="}, "debug": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/debug/-/debug-4.1.1.tgz", "integrity": "sha512-pYA<PERSON>zeRo8J6KPEaJ0VWOh5Pzkbw/RetuzehGM7QRRX5he4fPHx2rdKMB256ehJCkX+XRQm16eZLqLNS8RSZXZw==", "requires": {"ms": "^2.1.1"}}, "decimal.js": {"version": "9.0.1", "resolved": "https://registry.npmjs.org/decimal.js/-/decimal.js-9.0.1.tgz", "integrity": "sha512-2h0iKbJwnImBk4TGk7CG1xadoA0g3LDPlQhQzbZ221zvG0p2YVUedbKIPsOZXKZGx6YmZMJKYOalpCMxSdDqTQ=="}, "decode-uri-component": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/decode-uri-component/-/decode-uri-component-0.2.0.tgz", "integrity": "sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU="}, "deep-eql": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/deep-eql/-/deep-eql-3.0.1.tgz", "integrity": "sha512-+QeIQyN5ZuO+3Uk5DYh6/1eKO0m0YmJFGNmFHGACpf1ClL1nmlV/p4gNgbl2pJGxgXb4faqo6UE+M5ACEMyVcw==", "requires": {"type-detect": "^4.0.0"}}, "deep-metrics": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/deep-metrics/-/deep-metrics-0.0.1.tgz", "integrity": "sha512-732WmZgCWxOkf4QBvrCjPPuT6wTEzaGye/4JqYsU/sO0J53UNX4PBwK0JV262BZ5cxgLmKhU+NlrtKdPDgybkg==", "requires": {"semver": "^5.3.0"}, "dependencies": {"semver": {"version": "5.7.1", "resolved": "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz", "integrity": "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ=="}}}, "define-properties": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/define-properties/-/define-properties-1.1.3.tgz", "integrity": "sha512-3MqfYKj2lLzdMSf8ZIZE/V+Zuy+BgD6f164e8K2w7dgnpKArBDerGYpM46IYYcjnkdPNMjPk9A6VFB8+3SKlXQ==", "requires": {"object-keys": "^1.0.12"}}, "define-property": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/define-property/-/define-property-2.0.2.tgz", "integrity": "sha512-jwK2UV4cnPpbcG7+VRARKTZPUWowwXA8bzH5NP6ud0oeAxyYPuGZUAC7hMugpCdz4BeSZl2Dl9k66CHJ/46ZYQ==", "requires": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "dependencies": {"is-accessor-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ==", "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ==", "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg==", "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk="}, "depd": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/depd/-/depd-1.1.2.tgz", "integrity": "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak="}, "destroy": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/destroy/-/destroy-1.0.4.tgz", "integrity": "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA="}, "diagnostics": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/diagnostics/-/diagnostics-1.1.1.tgz", "integrity": "sha512-8wn1PmdunLJ9Tqbx+Fx/ZEuHfJf4NKSN2ZBj7SJC/OWRWha843+WsTjqMe1B5E3p28jqBlp+mJ2fPVxPyNgYKQ==", "requires": {"colorspace": "1.1.x", "enabled": "1.0.x", "kuler": "1.0.x"}}, "dicer": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/dicer/-/dicer-0.2.5.tgz", "integrity": "sha1-WZbAhrszIYyBLAkL3cCc0S+stw8=", "requires": {"readable-stream": "1.1.x", "streamsearch": "0.1.2"}, "dependencies": {"isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="}, "readable-stream": {"version": "1.1.14", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.1.14.tgz", "integrity": "sha1-fPTFTvZI44EwhMY23SB54WbAgdk=", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ="}}}, "diff": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/diff/-/diff-3.2.0.tgz", "integrity": "sha1-yc45Okt8vQsFinJck98pkCeGj/k=", "dev": true}, "dns-prefetch-control": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/dns-prefetch-control/-/dns-prefetch-control-0.1.0.tgz", "integrity": "sha1-YN20V3dOF48flBXwyrsOhbCzALI="}, "dont-sniff-mimetype": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/dont-sniff-mimetype/-/dont-sniff-mimetype-1.0.0.tgz", "integrity": "sha1-WTKJDcn04vGeXrAqIAJuXl78j1g="}, "dottie": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/dottie/-/dottie-2.0.2.tgz", "integrity": "sha512-fmrwR04lsniq/uSr8yikThDTrM7epXHBAAjH9TbeH3rEA8tdCO7mRzB9hdmdGyJCxF8KERo9CITcm3kGuoyMhg=="}, "double-ended-queue": {"version": "2.1.0-0", "resolved": "https://registry.npmjs.org/double-ended-queue/-/double-ended-queue-2.1.0-0.tgz", "integrity": "sha1-ED01J/0xUo9AGIEwyEHv3XgmTlw="}, "ecc-jsbn": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz", "integrity": "sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=", "requires": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}, "dependencies": {"jsbn": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/jsbn/-/jsbn-0.1.1.tgz", "integrity": "sha1-peZUwuWi3rXyAdls77yoDA7y9RM="}}}, "ecdsa-sig-formatter": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.11.tgz", "integrity": "sha512-nagl3RYrbNv6kQkeJIpt6NJZy8twLB/2vtz6yN9Z4vRKHN4/QZJIEbqohALSgwKdnksuY3k5Addp5lg8sVoVcQ==", "requires": {"safe-buffer": "^5.0.1"}}, "ee-first": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="}, "elastic-apm-http-client": {"version": "9.4.0", "resolved": "https://registry.npmjs.org/elastic-apm-http-client/-/elastic-apm-http-client-9.4.0.tgz", "integrity": "sha512-/jOZDyfzLNwHrNkPAI+AspLg0TXYXODWT+I1eoAWRCB7gP1vKvzUQAsP5iChodVqCbAj1eUNXB0KrvM6b07Thw==", "requires": {"breadth-filter": "^2.0.0", "container-info": "^1.0.1", "end-of-stream": "^1.4.4", "fast-safe-stringify": "^2.0.7", "fast-stream-to-buffer": "^1.0.0", "pump": "^3.0.0", "readable-stream": "^3.4.0", "stream-chopper": "^3.0.1", "unicode-byte-truncate": "^1.0.0"}}, "elastic-apm-node": {"version": "3.5.0", "resolved": "https://registry.npmjs.org/elastic-apm-node/-/elastic-apm-node-3.5.0.tgz", "integrity": "sha512-s0masF1sJSrUFwydd2Nrk888BRA/YSxUvQFSrO2Pbd69+iqnjC9Yd9yQvAwfaI0HF4ViVqDXqmmZ1l3FqYgb1Q==", "requires": {"after-all-results": "^2.0.0", "async-value-promise": "^1.1.1", "basic-auth": "^2.0.1", "console-log-level": "^1.4.1", "cookie": "^0.4.0", "core-util-is": "^1.0.2", "elastic-apm-http-client": "^9.3.0", "end-of-stream": "^1.4.4", "error-stack-parser": "^2.0.6", "fast-safe-stringify": "^2.0.7", "http-headers": "^3.0.2", "http-request-to-url": "^1.0.0", "is-native": "^1.0.1", "measured-reporting": "^1.51.1", "monitor-event-loop-delay": "^1.0.0", "object-filter-sequence": "^1.0.0", "object-identity-map": "^1.0.2", "original-url": "^1.2.3", "read-pkg-up": "^7.0.1", "redact-secrets": "^1.0.0", "relative-microtime": "^2.0.0", "require-ancestors": "^1.0.0", "require-in-the-middle": "^5.0.3", "semver": "^6.3.0", "set-cookie-serde": "^1.0.0", "shallow-clone-shim": "^2.0.0", "sql-summary": "^1.0.1", "stackman": "^4.0.0", "traceparent": "^1.0.0", "unicode-byte-truncate": "^1.0.0"}}, "emitter-listener": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/emitter-listener/-/emitter-listener-1.1.2.tgz", "integrity": "sha512-Bt1sBAGFHY9DKY+4/2cV6izcKJUf5T7/gkdmkxzX/qv9CcGH8xSwVRW5mtX03SWJtRTWSOpzCuWN9rBFYZepZQ==", "requires": {"shimmer": "^1.2.0"}}, "enabled": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/enabled/-/enabled-1.0.2.tgz", "integrity": "sha1-ll9lE9LC0cX0ZStkouM5ZGf8L5M=", "requires": {"env-variable": "0.0.x"}}, "encodeurl": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz", "integrity": "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k="}, "end-of-stream": {"version": "1.4.4", "resolved": "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz", "integrity": "sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==", "requires": {"once": "^1.4.0"}}, "engine.io": {"version": "3.1.5", "resolved": "https://registry.npmjs.org/engine.io/-/engine.io-3.1.5.tgz", "integrity": "sha512-D06ivJkYxyRrcEe0bTpNnBQNgP9d3xog+qZlLbui8EsMr/DouQpf5o9FzJnWYHEYE0YsFHllUv2R1dkgYZXHcA==", "requires": {"accepts": "~1.3.4", "base64id": "1.0.0", "cookie": "0.3.1", "debug": "~3.1.0", "engine.io-parser": "~2.1.0", "uws": "~9.14.0", "ws": "~3.3.1"}, "dependencies": {"cookie": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/cookie/-/cookie-0.3.1.tgz", "integrity": "sha1-5+Ch+e9DtMi6klxcWpboBtFoc7s="}, "debug": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/debug/-/debug-3.1.0.tgz", "integrity": "sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "ws": {"version": "3.3.3", "resolved": "https://registry.npmjs.org/ws/-/ws-3.3.3.tgz", "integrity": "sha512-nnWLa/NwZSt4KQJu51MYlCcSQ5g7INpOrOMt4XV8j4dqTXdmlUmSHQ8/oLC069ckre0fRsgfvsKwbTdtKLCDkA==", "requires": {"async-limiter": "~1.0.0", "safe-buffer": "~5.1.0", "ultron": "~1.1.0"}}}}, "engine.io-client": {"version": "3.3.2", "resolved": "https://registry.npmjs.org/engine.io-client/-/engine.io-client-3.3.2.tgz", "integrity": "sha512-y0CPINnhMvPuwtqXfsGuWE8BB66+B6wTtCofQDRecMQPYX3MYUZXFNKDhdrSe3EVjgOu4V3rxdeqN/Tr91IgbQ==", "requires": {"component-emitter": "1.2.1", "component-inherit": "0.0.3", "debug": "~3.1.0", "engine.io-parser": "~2.1.1", "has-cors": "1.1.0", "indexof": "0.0.1", "parseqs": "0.0.5", "parseuri": "0.0.5", "ws": "~6.1.0", "xmlhttprequest-ssl": "~1.5.4", "yeast": "0.1.2"}, "dependencies": {"debug": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/debug/-/debug-3.1.0.tgz", "integrity": "sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "engine.io-parser": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/engine.io-parser/-/engine.io-parser-2.1.3.tgz", "integrity": "sha512-6HXPre2O4Houl7c4g7Ic/XzPnHBvaEmN90vtRO9uLmwtRqQmTOw0QMevL1TOfL2Cpu1VzsaTmMotQgMdkzGkVA==", "requires": {"after": "0.8.2", "arraybuffer.slice": "~0.0.7", "base64-arraybuffer": "0.1.5", "blob": "0.0.5", "has-binary2": "~1.0.2"}}, "env-variable": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/env-variable/-/env-variable-0.0.6.tgz", "integrity": "sha512-bHz59NlBbtS0NhftmR8+ExBEekE7br0e01jw+kk0NDro7TtZzBYZ5ScGPs3OmwnpyfHTHOtr1Y6uedCdrIldtg=="}, "error-callsites": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/error-callsites/-/error-callsites-2.0.3.tgz", "integrity": "sha512-v036z4IEffZFE5kBkV5/F2MzhLnG0vuDyN+VXpzCf4yWXvX/1WJCI0A+TGTr8HWzBfCw5k8gr9rwAo09V+obTA=="}, "error-ex": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz", "integrity": "sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==", "requires": {"is-arrayish": "^0.2.1"}}, "error-stack-parser": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/error-stack-parser/-/error-stack-parser-2.0.6.tgz", "integrity": "sha512-d51brTeqC+BHlwF0BhPtcYgF5nlzf9ZZ0ZIUQNZpc9ZB9qw5IJ2diTrBY9jlCJkTLITYPjmiX6OWCwH+fuyNgQ==", "requires": {"stackframe": "^1.1.1"}}, "es-abstract": {"version": "1.17.6", "resolved": "https://registry.npmjs.org/es-abstract/-/es-abstract-1.17.6.tgz", "integrity": "sha512-Fr89bON3WFyUi5EvAeI48QTWX0AyekGgLA8H+c+7fbfCkJwRWRMLd8CQedNEyJuoYYhmtEqY92pgte1FAhBlhw==", "requires": {"es-to-primitive": "^1.2.1", "function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.1", "is-callable": "^1.2.0", "is-regex": "^1.1.0", "object-inspect": "^1.7.0", "object-keys": "^1.1.1", "object.assign": "^4.1.0", "string.prototype.trimend": "^1.0.1", "string.prototype.trimstart": "^1.0.1"}}, "es-to-primitive": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/es-to-primitive/-/es-to-primitive-1.2.1.tgz", "integrity": "sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==", "requires": {"is-callable": "^1.1.4", "is-date-object": "^1.0.1", "is-symbol": "^1.0.2"}}, "escape-html": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg="}, "escape-latex": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/escape-latex/-/escape-latex-1.2.0.tgz", "integrity": "sha512-nV5aVWW1K0wEiUIEdZ4erkGGH8mDxGyxSeqPzRNtWP7ataw+/olFObw7hujFWlVjNsaDFw5VZ5NzVSIqRgfTiw=="}, "escape-regexp": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/escape-regexp/-/escape-regexp-0.0.1.tgz", "integrity": "sha1-9EvaEtRbvfnLf4Yu5+SCez3TIlQ="}, "escape-string-regexp": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="}, "esprima": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz", "integrity": "sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A=="}, "etag": {"version": "1.8.1", "resolved": "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc="}, "eventemitter2": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/eventemitter2/-/eventemitter2-1.0.5.tgz", "integrity": "sha1-+YNhBRexc3wLncZDvsqTiTwE3xg="}, "events": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/events/-/events-1.1.1.tgz", "integrity": "sha1-nr23Y1rQmccNzEwqH1AEKI6L2SQ="}, "expand-brackets": {"version": "2.1.4", "resolved": "https://registry.npmjs.org/expand-brackets/-/expand-brackets-2.1.4.tgz", "integrity": "sha1-t3c14xXOMPa27/D4OwQVGiJEliI=", "requires": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "expect-ct": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/expect-ct/-/expect-ct-0.1.0.tgz", "integrity": "sha1-UnNWeN4YUwiQ2Ne5XwrGNkCVgJQ="}, "express": {"version": "4.16.2", "resolved": "https://registry.npmjs.org/express/-/express-4.16.2.tgz", "integrity": "sha1-41xt/i1kt9ygpc1PIXgb4ymeB2w=", "requires": {"accepts": "~1.3.4", "array-flatten": "1.1.1", "body-parser": "1.18.2", "content-disposition": "0.5.2", "content-type": "~1.0.4", "cookie": "0.3.1", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "~1.1.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "1.1.0", "fresh": "0.5.2", "merge-descriptors": "1.0.1", "methods": "~1.1.2", "on-finished": "~2.3.0", "parseurl": "~1.3.2", "path-to-regexp": "0.1.7", "proxy-addr": "~2.0.2", "qs": "6.5.1", "range-parser": "~1.2.0", "safe-buffer": "5.1.1", "send": "0.16.1", "serve-static": "1.13.1", "setprototypeof": "1.1.0", "statuses": "~1.3.1", "type-is": "~1.6.15", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "dependencies": {"cookie": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/cookie/-/cookie-0.3.1.tgz", "integrity": "sha1-5+Ch+e9DtMi6klxcWpboBtFoc7s="}, "debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "safe-buffer": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.1.tgz", "integrity": "sha512-kKvNJn6Mm93gAczWVJg7wH+wGYWNrDHdWvpUmHyEsgCtIwwo3bqPtV4tR5tuPaUhTOo/kvhVwd8XwwOllGYkbg=="}}}, "extend": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz", "integrity": "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g=="}, "extend-shallow": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-3.0.2.tgz", "integrity": "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=", "requires": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "dependencies": {"is-extendable": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-extendable/-/is-extendable-1.0.1.tgz", "integrity": "sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA==", "requires": {"is-plain-object": "^2.0.4"}}}}, "extglob": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/extglob/-/extglob-2.0.4.tgz", "integrity": "sha512-Nmb6QXkELsuBr24CJSkilo6UHHgbekK5UiZgfE6UHD3Eb27YC6oD+bhcT+tJ6cl8dmsgdQxnWlcry8ksBIBLpw==", "requires": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/define-property/-/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "requires": {"is-descriptor": "^1.0.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ==", "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ==", "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg==", "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "extsprintf": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/extsprintf/-/extsprintf-1.3.0.tgz", "integrity": "sha1-lpGEQOMEGnpBT4xS48V06zw+HgU="}, "fast-deep-equal": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="}, "fast-json-stable-stringify": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw=="}, "fast-safe-stringify": {"version": "2.0.7", "resolved": "https://registry.npmjs.org/fast-safe-stringify/-/fast-safe-stringify-2.0.7.tgz", "integrity": "sha512-Utm6CdzT+6xsDk2m8S6uL8VHxNwI6Jub+e9NYTcAms28T84pTa25GJQV9j0CY0N1rM8hK4x6grpF2BQf+2qwVA=="}, "fast-stream-to-buffer": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/fast-stream-to-buffer/-/fast-stream-to-buffer-1.0.0.tgz", "integrity": "sha512-bI/544WUQlD2iXBibQbOMSmG07Hay7YrpXlKaeGTPT7H7pC0eitt3usak5vUwEvCGK/O7rUAM3iyQValGU22TQ==", "requires": {"end-of-stream": "^1.4.1"}}, "fclone": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/fclone/-/fclone-1.0.11.tgz", "integrity": "sha1-EOhdo4v+p/xZk0HClu4ddyZu5kA="}, "fd-slicer": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/fd-slicer/-/fd-slicer-1.1.0.tgz", "integrity": "sha1-JcfInLH5B3+IkbvmHY85Dq4lbx4=", "requires": {"pend": "~1.2.0"}}, "fecha": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/fecha/-/fecha-4.2.0.tgz", "integrity": "sha512-aN3pcx/DSmtyoovUudctc8+6Hl4T+hI9GBBHLjA76jdZl7+b1sgh5g4k+u/GL3dTy1/pnYzKp69FpJ0OicE3Wg=="}, "file-uri-to-path": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz", "integrity": "sha512-0Zt+s3L7Vf1biwWZ29aARiVYLx7iMGnEUl9x33fbB/j3jR81u/O2LbqK+Bm1CDSNDKVtJ/YjwY7TUd5SkeLQLw==", "optional": true}, "fill-range": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/fill-range/-/fill-range-4.0.0.tgz", "integrity": "sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=", "requires": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}}}, "finalhandler": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.1.0.tgz", "integrity": "sha1-zgtoVbRYU+eRsvzGgARtiCU91/U=", "requires": {"debug": "2.6.9", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "parseurl": "~1.3.2", "statuses": "~1.3.1", "unpipe": "~1.0.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "find-up": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz", "integrity": "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==", "requires": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}}, "flat": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/flat/-/flat-5.0.0.tgz", "integrity": "sha512-6KSMM+cHHzXC/hpldXApL2S8Uz+QZv+tq5o/L0KQYleoG+GcwrnIJhTWC7tCOiKQp8D/fIvryINU1OZCCwevjA==", "requires": {"is-buffer": "~2.0.4"}}, "for-in": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/for-in/-/for-in-1.0.2.tgz", "integrity": "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA="}, "forever-agent": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/forever-agent/-/forever-agent-0.6.1.tgz", "integrity": "sha1-+8cfDEGt6zf5bFd60e1C2P2sypE="}, "form-data": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/form-data/-/form-data-2.3.3.tgz", "integrity": "sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ==", "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}}, "formidable": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/formidable/-/formidable-1.2.2.tgz", "integrity": "sha512-V8gLm+41I/8kguQ4/o1D3RIHRmhYFG4pnNyonvua+40rqcEmT4+V71yaZ3B457xbbgCsCfjSPi65u/W6vK1U5Q=="}, "forwarded": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/forwarded/-/forwarded-0.1.2.tgz", "integrity": "sha1-mMI9qxF1ZXuMBXPozszZGw/xjIQ="}, "forwarded-parse": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/forwarded-parse/-/forwarded-parse-2.1.0.tgz", "integrity": "sha512-as9a7Xelt0CvdUy7/qxrY73dZq2vMx49F556fwjjFrUyzq5uHHfeLgD2cCq/6P4ZvusGZzjD6aL2NdgGdS5Cew=="}, "fraction.js": {"version": "4.0.4", "resolved": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.0.4.tgz", "integrity": "sha512-aK/oGatyYLTtXRHjfEsytX5fieeR5H4s8sLorzcT12taFS+dbMZejnvm9gRa8mZAPwci24ucjq9epDyaq5u8Iw=="}, "fragment-cache": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/fragment-cache/-/fragment-cache-0.2.1.tgz", "integrity": "sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=", "requires": {"map-cache": "^0.2.2"}}, "frameguard": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/frameguard/-/frameguard-3.0.0.tgz", "integrity": "sha1-e8rUae57lukdEs6zlZx4I1qScuk="}, "fresh": {"version": "0.5.2", "resolved": "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz", "integrity": "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac="}, "fs.realpath": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="}, "fsevents": {"version": "1.2.13", "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-1.2.13.tgz", "integrity": "sha512-oWb1Z6mkHIskLzEJ/XWX0srkpkTQ7vaopMQkyaEIoq0fmtFVxOthb8cCxeT+p3ynTdkk/RZwbgG4brR5BeWECw==", "optional": true, "requires": {"bindings": "^1.5.0", "nan": "^2.12.1"}}, "function-bind": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz", "integrity": "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A=="}, "generic-pool": {"version": "2.4.3", "resolved": "https://registry.npmjs.org/generic-pool/-/generic-pool-2.4.3.tgz", "integrity": "sha1-eAw29p360FpaBF3Te+etyhGk9v8="}, "geoip-lite": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/geoip-lite/-/geoip-lite-1.4.1.tgz", "integrity": "sha512-uhPTk7ndEAnqpvNhcSQh51dIrMTGOmOPKIi4KgIHtuJbgaAGgON1Q1UFGhu4YSCnN1DVWTLqcpoL9/746x6Nqg==", "requires": {"async": "^2.1.1", "colors": "^1.1.2", "iconv-lite": "^0.4.13", "ip-address": "^5.8.9", "lazy": "^1.0.11", "rimraf": "^2.5.2", "yauzl": "^2.9.2"}}, "get-func-name": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/get-func-name/-/get-func-name-2.0.0.tgz", "integrity": "sha1-6td0q+5y4gQJQzoGY2YCPdaIekE="}, "get-value": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/get-value/-/get-value-2.0.6.tgz", "integrity": "sha1-3BXKHGcjh8p2vTesCjlbogQqLCg="}, "getpass": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/getpass/-/getpass-0.1.7.tgz", "integrity": "sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=", "requires": {"assert-plus": "^1.0.0"}}, "gkt": {"version": "https://tgz.pm2.io/gkt-1.0.0.tgz", "integrity": "sha512-zr6QQnzLt3Ja0t0XI8gws2kn7zV2p0l/D3kreNvS6hFZhVU5g+uY/30l42jbgt0XGcNBEmBDGJR71J692V92tA==", "optional": true}, "glob": {"version": "7.1.6", "resolved": "https://registry.npmjs.org/glob/-/glob-7.1.6.tgz", "integrity": "sha512-LwaxwyZ72Lk7vZINtNNrywX0ZuLyStrdDtabefZKAY5ZGJhVtgdznluResxNmPitE0SAO+O26sWTHeKSI2wMBA==", "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "glob-parent": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-3.1.0.tgz", "integrity": "sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=", "requires": {"is-glob": "^3.1.0", "path-dirname": "^1.0.0"}, "dependencies": {"is-glob": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-3.1.0.tgz", "integrity": "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=", "requires": {"is-extglob": "^2.1.0"}}}}, "graceful-fs": {"version": "4.2.4", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.4.tgz", "integrity": "sha512-WjKPNJF79dtJAVniUlGGWHYGz2jWxT6VhN/4m1NdkbZ2nOsEF+cI1Edgql5zCRhs/VsQYRvrXctxktVXZUkixw=="}, "graceful-readlink": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/graceful-readlink/-/graceful-readlink-1.0.1.tgz", "integrity": "sha1-TK+tdrxi8C+gObL5Tpo906ORpyU=", "dev": true}, "graphlib": {"version": "2.1.8", "resolved": "https://registry.npmjs.org/graphlib/-/graphlib-2.1.8.tgz", "integrity": "sha512-jcLLfkpoVGmH7/InMC/1hIvOPSUh38oJtGhvrOFGzioE1DZ+0YW16RgmOJhHiuWTvGiJQ9Z1Ik43JvkRPRvE+A==", "requires": {"lodash": "^4.17.15"}, "dependencies": {"lodash": {"version": "4.17.20", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.20.tgz", "integrity": "sha512-PlhdFcillOINfeV7Ni6oF1TAEayyZBoZ8bcshTHqOYJYlrqzRK5hagpagky5o4HfCzzd1TRkXPMFq6cKk9rGmA=="}}}, "growl": {"version": "1.9.2", "resolved": "https://registry.npmjs.org/growl/-/growl-1.9.2.tgz", "integrity": "sha1-Dqd0NxXbjY3ixe3hd14bRayFwC8=", "dev": true}, "har-schema": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/har-schema/-/har-schema-2.0.0.tgz", "integrity": "sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI="}, "har-validator": {"version": "5.1.5", "resolved": "https://registry.npmjs.org/har-validator/-/har-validator-5.1.5.tgz", "integrity": "sha512-nmT2T0lljbxdQZfspsno9hgrG3Uir6Ks5afism62poxqBM6sDnMEuPmzTq8XN0OEwqKLLdh1jQI3qyE66Nzb3w==", "requires": {"ajv": "^6.12.3", "har-schema": "^2.0.0"}}, "has": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/has/-/has-1.0.3.tgz", "integrity": "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==", "requires": {"function-bind": "^1.1.1"}}, "has-ansi": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/has-ansi/-/has-ansi-2.0.0.tgz", "integrity": "sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=", "requires": {"ansi-regex": "^2.0.0"}}, "has-binary2": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/has-binary2/-/has-binary2-1.0.3.tgz", "integrity": "sha512-G1LWKhDSvhGeAQ8mPVQlqNcOB2sJdwATtZKl2pDKKHfpf/rYj24lkinxf69blJbnsvtqqNU+L3SL50vzZhXOnw==", "requires": {"isarray": "2.0.1"}, "dependencies": {"isarray": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-2.0.1.tgz", "integrity": "sha1-o32U7ZzaLVmGXJ92/llu4fM4dB4="}}}, "has-cors": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/has-cors/-/has-cors-1.1.0.tgz", "integrity": "sha1-XkdHk/fqmEPRu5nCPu9J/xJv/zk="}, "has-flag": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0="}, "has-symbols": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.1.tgz", "integrity": "sha512-PLcsoqu++dmEIZB+6totNFKq/7Do+Z0u4oT0zKOJNl3lYK6vGwwu2hjHs+68OEZbTjiUE9bgOABXbP/GvrS0Kg=="}, "has-value": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/has-value/-/has-value-1.0.0.tgz", "integrity": "sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=", "requires": {"get-value": "^2.0.6", "has-values": "^1.0.0", "isobject": "^3.0.0"}}, "has-values": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/has-values/-/has-values-1.0.0.tgz", "integrity": "sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=", "requires": {"is-number": "^3.0.0", "kind-of": "^4.0.0"}, "dependencies": {"is-buffer": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w=="}, "kind-of": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-4.0.0.tgz", "integrity": "sha1-IIE989cSkosgc3hpGkUGb65y3Vc=", "requires": {"is-buffer": "^1.1.5"}}}}, "hawk": {"version": "6.0.2", "resolved": "https://registry.npmjs.org/hawk/-/hawk-6.0.2.tgz", "integrity": "sha512-miowhl2+U7Qle4vdLqDdPt9m09K6yZhkLDTWGoUiUzrQCn+mHHSmfJgAyGaLRZbPmTqfFFjRV1QWCW0VWUJBbQ==", "requires": {"boom": "4.x.x", "cryptiles": "3.x.x", "hoek": "4.x.x", "sntp": "2.x.x"}}, "he": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/he/-/he-1.1.1.tgz", "integrity": "sha1-k0EP0hsAlzUVH4howvJx80J+I/0=", "dev": true}, "helmet": {"version": "3.12.0", "resolved": "https://registry.npmjs.org/helmet/-/helmet-3.12.0.tgz", "integrity": "sha512-CgkctpvreQLL6X3EL2Igs/92+75ZFIsrob9/Rdwf2hQCBGH/DxLk4xFPxAAl6jYnnus/YXfFEVXHEJf8TJTwlA==", "requires": {"dns-prefetch-control": "0.1.0", "dont-sniff-mimetype": "1.0.0", "expect-ct": "0.1.0", "frameguard": "3.0.0", "helmet-csp": "2.7.0", "hide-powered-by": "1.0.0", "hpkp": "2.0.0", "hsts": "2.1.0", "ienoopen": "1.0.0", "nocache": "2.0.0", "referrer-policy": "1.1.0", "x-xss-protection": "1.1.0"}}, "helmet-csp": {"version": "2.7.0", "resolved": "https://registry.npmjs.org/helmet-csp/-/helmet-csp-2.7.0.tgz", "integrity": "sha512-IGIAkWnxjRbgMXFA2/kmDqSIrIaSfZ6vhMHlSHw7jm7Gm9nVVXqwJ2B1YEpYrJsLrqY+w2Bbimk7snux9+sZAw==", "requires": {"camelize": "1.0.0", "content-security-policy-builder": "2.0.0", "dasherize": "2.0.0", "lodash.reduce": "4.6.0", "platform": "1.3.5"}}, "hide-powered-by": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/hide-powered-by/-/hide-powered-by-1.0.0.tgz", "integrity": "sha1-SoWtZYgfYoV/xwr3F0oRhNzM4ys="}, "hoek": {"version": "4.2.1", "resolved": "https://registry.npmjs.org/hoek/-/hoek-4.2.1.tgz", "integrity": "sha512-QLg82fGkfnJ/4iy1xZ81/9SIJiq1NGFUMGs6ParyjBZr6jW2Ufj/snDqTHixNlHdPNwN2RLVD0Pi3igeK9+JfA=="}, "hollaex-node-lib": {"version": "github:bitholla/hollaex-node-lib#48bee2cf3f7d7ea0d000598df1f3d16c307d6b7a", "from": "github:bitholla/hollaex-node-lib#2.0", "requires": {"lodash": "4.17.13", "moment": "2.24.0", "request": "2.88.0", "request-promise": "4.2.2", "socket.io-client": "2.2.0"}, "dependencies": {"lodash": {"version": "4.17.13", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.13.tgz", "integrity": "sha512-vm3/XWXfWtRua0FkUyEHBZy8kCPjErNBT9fJx8Zvs+U6zjqPbTUOpkaoum3O5uiA8sm+yNMHXfYkTUHFoMxFNA=="}, "moment": {"version": "2.24.0", "resolved": "https://registry.npmjs.org/moment/-/moment-2.24.0.tgz", "integrity": "sha512-bV7f+6l2QigeBBZSM/6yTNq4P2fNpSWj/0e7jQcy87A8e7o2nAfP/34/2ky5Vw4B9S446EtIhodAzkFCcR4dQg=="}, "qs": {"version": "6.5.2", "resolved": "https://registry.npmjs.org/qs/-/qs-6.5.2.tgz", "integrity": "sha512-N5ZAX4/LxJmF+7wN74pUD6qAh9/wnvdQcjq9TZjevvXzSUo7bfmw91saqMjzGS2xq91/odN2dW/WOl7qQHNDGA=="}, "request": {"version": "2.88.0", "resolved": "https://registry.npmjs.org/request/-/request-2.88.0.tgz", "integrity": "sha512-NAqBSrijGLZdM0WZNsInLJpkJokL72XYjUpnB0iwsRgxh7dB6COrHnTBNwN0E+lHDAJzu7kLAkDeY08z2/A0hg==", "requires": {"aws-sign2": "~0.7.0", "aws4": "^1.8.0", "caseless": "~0.12.0", "combined-stream": "~1.0.6", "extend": "~3.0.2", "forever-agent": "~0.6.1", "form-data": "~2.3.2", "har-validator": "~5.1.0", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "performance-now": "^2.1.0", "qs": "~6.5.2", "safe-buffer": "^5.1.2", "tough-cookie": "~2.4.3", "tunnel-agent": "^0.6.0", "uuid": "^3.3.2"}}, "uuid": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/uuid/-/uuid-3.4.0.tgz", "integrity": "sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A=="}}}, "hollaex-tools-lib": {"version": "github:bitholla/hollaex-tools-lib#3ca999c585417bbf7caf5b5d75840abd48e05660", "from": "github:bitholla/hollaex-tools-lib#develop", "requires": {"bcryptjs": "2.4.3", "bluebird": "3.7.2", "flat": "5.0.0", "hollaex-node-lib": "github:bitholla/hollaex-node-lib#2.0", "json2csv": "4.5.4", "jsonwebtoken": "8.5.1", "lodash": "4.17.15", "otp": "0.1.3", "request": "2.83.0", "request-promise": "4.2.2", "validator": "13.1.1"}, "dependencies": {"bluebird": {"version": "3.7.2", "resolved": "https://registry.npmjs.org/bluebird/-/bluebird-3.7.2.tgz", "integrity": "sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg=="}, "jsonwebtoken": {"version": "8.5.1", "resolved": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-8.5.1.tgz", "integrity": "sha512-XjwVfRS6jTMsqYs0EsuJ4LGxXV14zQybNd4L2r0UvbVnSF9Af8x7p5MzbJ90Ioz/9TI41/hTCvznF/loiSzn8w==", "requires": {"jws": "^3.2.2", "lodash.includes": "^4.3.0", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isnumber": "^3.0.3", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.once": "^4.0.0", "ms": "^2.1.1", "semver": "^5.6.0"}}, "lodash": {"version": "4.17.15", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.15.tgz", "integrity": "sha512-8xOcRHvCjnocdS5cpwXQXVzmmh5e5+saE2QGoeQmbKmRS6J3VQppPOIt0MnmE+4xlZoumy0GPG0D0MVIQbNA1A=="}, "semver": {"version": "5.7.1", "resolved": "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz", "integrity": "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ=="}, "validator": {"version": "13.1.1", "resolved": "https://registry.npmjs.org/validator/-/validator-13.1.1.tgz", "integrity": "sha512-8GfPiwzzRoWTg7OV1zva1KvrSemuMkv07MA9TTl91hfhe+wKrsrgVN4H2QSFd/U/FhiU3iWPYVgvbsOGwhyFWw=="}}}, "hosted-git-info": {"version": "2.8.8", "resolved": "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-2.8.8.tgz", "integrity": "sha512-f/wzC2QaWBs7t9IYqB4T3sR1xviIViXJRJTWBlx2Gf3g0Xi5vI7Yy4koXQ1c9OYDGHN9sBy1DQ2AB8fqZBWhUg=="}, "hpkp": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/hpkp/-/hpkp-2.0.0.tgz", "integrity": "sha1-EOFCJk52IVpdMMROxD3mTe5tFnI="}, "hsts": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/hsts/-/hsts-2.1.0.tgz", "integrity": "sha512-zXhh/DqgrTXJ7erTN6Fh5k/xjMhDGXCqdYN3wvxUvGUQvnxcFfUd8E+6vLg/nk3ss1TYMb+DhRl25fYABioTvA=="}, "http": {"version": "0.0.0", "resolved": "https://registry.npmjs.org/http/-/http-0.0.0.tgz", "integrity": "sha1-huYybSnF0Dnen6xYSkVon5KfT3I="}, "http-errors": {"version": "1.6.3", "resolved": "https://registry.npmjs.org/http-errors/-/http-errors-1.6.3.tgz", "integrity": "sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=", "requires": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.0", "statuses": ">= 1.4.0 < 2"}, "dependencies": {"inherits": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="}, "statuses": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/statuses/-/statuses-1.5.0.tgz", "integrity": "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow="}}}, "http-headers": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/http-headers/-/http-headers-3.0.2.tgz", "integrity": "sha512-87E1I+2Wg4dxxz4rcxElo3dxO/w1ZtgL1yA0Sb6vH3qU16vRKq1NjWQv9SCY3ly2OQROcoxHZOUpmelS+k6wOw==", "requires": {"next-line": "^1.1.0"}}, "http-request-to-url": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/http-request-to-url/-/http-request-to-url-1.0.0.tgz", "integrity": "sha512-YYx0lKXG9+T1fT2q3ZgXLczMI3jW09g9BvIA6L3BG0tFqGm83Ka/+RUZGANRG7Ut/yueD7LPcZQ/+pA5ndNajw==", "requires": {"await-event": "^2.1.0", "socket-location": "^1.0.0"}}, "http-signature": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/http-signature/-/http-signature-1.2.0.tgz", "integrity": "sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=", "requires": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}}, "iconv-lite": {"version": "0.4.19", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.19.tgz", "integrity": "sha512-oTZqweIP51xaGPI4uPa56/Pri/480R+mo7SeU+YETByQNhDG55ycFyNLIgta9vXhILrxXDmF7ZGhqZIcuN0gJQ=="}, "ieee754": {"version": "1.1.13", "resolved": "https://registry.npmjs.org/ieee754/-/ieee754-1.1.13.tgz", "integrity": "sha512-4vf7I2LYV/HaWerSo3XmlMkp5eZ83i+/CDluXi/IGTs/O1sejBNhTtnxzmRZfvOUqj7lZjqHkeTvpgSFDlWZTg=="}, "ienoopen": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/ienoopen/-/ienoopen-1.0.0.tgz", "integrity": "sha1-NGpCj0dKrI9QzzeE6i0PFvYr2ms="}, "in-publish": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/in-publish/-/in-publish-2.0.1.tgz", "integrity": "sha512-oDM0kUSNFC31ShNxHKUyfZKy8ZeXZBWMjMdZHKLOk13uvT27VTL/QzRGfRUcevJhpkZAvlhPYuXkF7eNWrtyxQ=="}, "indexof": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/indexof/-/indexof-0.0.1.tgz", "integrity": "sha1-gtwzbSMrkGIXnQWrMpOmYFn9Q10="}, "inflection": {"version": "1.12.0", "resolved": "https://registry.npmjs.org/inflection/-/inflection-1.12.0.tgz", "integrity": "sha1-ogCTVlbW9fa8TcdQLhrstwMihBY="}, "inflight": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="}, "install": {"version": "0.10.4", "resolved": "https://registry.npmjs.org/install/-/install-0.10.4.tgz", "integrity": "sha512-+IRyOastuPmLVx9zlVXJoKErSqz1Ma5at9A7S8yfsj3W+Kg95faPoh3bPDtMrZ/grz4PRmXzrswmlzfLlYyLOw=="}, "interpret": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/interpret/-/interpret-1.4.0.tgz", "integrity": "sha512-agE4QfB2Lkp9uICn7BAqoscw4SZP9kTE2hxiFI3jBPmXJfdqiahTbUuKGsMoN2GtqL9AxhYioAcVvgsb1HvRbA=="}, "ip-address": {"version": "5.9.4", "resolved": "https://registry.npmjs.org/ip-address/-/ip-address-5.9.4.tgz", "integrity": "sha512-dHkI3/YNJq4b/qQaz+c8LuarD3pY24JqZWfjB8aZx1gtpc2MDILu9L9jpZe1sHpzo/yWFweQVn+U//FhazUxmw==", "requires": {"jsbn": "1.1.0", "lodash": "^4.17.15", "sprintf-js": "1.1.2"}, "dependencies": {"lodash": {"version": "4.17.20", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.20.tgz", "integrity": "sha512-PlhdFcillOINfeV7Ni6oF1TAEayyZBoZ8bcshTHqOYJYlrqzRK5hagpagky5o4HfCzzd1TRkXPMFq6cKk9rGmA=="}}}, "ipaddr.js": {"version": "1.9.1", "resolved": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "integrity": "sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g=="}, "is": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/is/-/is-3.3.0.tgz", "integrity": "sha512-nW24QBoPcFGGHJGUwnfpI7Yc5CdqWNdsyHQszVE/z2pKHXzh7FZ5GWhJqSyaQ9wMkQnsTx+kAI8bHlCX4tKdbg=="}, "is-accessor-descriptor": {"version": "0.1.6", "resolved": "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-0.1.6.tgz", "integrity": "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=", "requires": {"kind-of": "^3.0.2"}, "dependencies": {"is-buffer": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w=="}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "is-arrayish": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz", "integrity": "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0="}, "is-binary-path": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-1.0.1.tgz", "integrity": "sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=", "requires": {"binary-extensions": "^1.0.0"}}, "is-bluebird": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-bluebird/-/is-bluebird-1.0.2.tgz", "integrity": "sha1-CWQ5Bg9KpBGr7hkUOoTWpVNG1uI="}, "is-buffer": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-2.0.4.tgz", "integrity": "sha512-Kq1rokWXOPXWuaMAqZiJW4XxsmD9zGx9q4aePabbn3qCRGedtH7Cm+zV8WETitMfu1wdh+Rvd6w5egwSngUX2A=="}, "is-callable": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/is-callable/-/is-callable-1.2.0.tgz", "integrity": "sha512-pyVD9AaGLxtg6srb2Ng6ynWJqkHU9bEM087AKck0w8QwDarTfNcpIYoU8x8Hv2Icm8u6kFJM18Dag8lyqGkviw=="}, "is-data-descriptor": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-0.1.4.tgz", "integrity": "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=", "requires": {"kind-of": "^3.0.2"}, "dependencies": {"is-buffer": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w=="}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "is-date-object": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-date-object/-/is-date-object-1.0.2.tgz", "integrity": "sha512-USlDT524woQ08aoZFzh3/Z6ch9Y/EWXEHQ/AaRN0SkKq4t2Jw2R2339tSXmwuVoY7LLlBCbOIlx2myP/L5zk0g=="}, "is-descriptor": {"version": "0.1.6", "resolved": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-0.1.6.tgz", "integrity": "sha512-avDYr0SB3DwO9zsMov0gKCESFYqCnE4hq/4z3TdUlukEy5t9C0YRq7HLrsN52NAcqXKaepeCD0n+B0arnVG3Hg==", "requires": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "dependencies": {"kind-of": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-5.1.0.tgz", "integrity": "sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw=="}}}, "is-extendable": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/is-extendable/-/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik="}, "is-extglob": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI="}, "is-finite": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/is-finite/-/is-finite-1.1.0.tgz", "integrity": "sha512-cdyMtqX/BOqqNBBiKlIVkytNHm49MtMlYyn1zxzvJKWmFMlGzm+ry5BBfYyeY9YmNKbRSo/o7OX9w9ale0wg3w=="}, "is-glob": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.1.tgz", "integrity": "sha512-5G0tKtBTFImOqDnLB2hG6Bp2qcKEFduo4tZu9MT/H6NQv/ghhy30o55ufafxJ/LdH79LLs2Kfrn85TLKyA7BUg==", "requires": {"is-extglob": "^2.1.1"}}, "is-integer": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/is-integer/-/is-integer-1.0.7.tgz", "integrity": "sha1-a96Bqs3feLZZtmKdYpytxRqIbVw=", "requires": {"is-finite": "^1.0.0"}}, "is-native": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-native/-/is-native-1.0.1.tgz", "integrity": "sha1-zRjMFi6EUNaDtbq+eayZwUVElnU=", "requires": {"is-nil": "^1.0.0", "to-source-code": "^1.0.0"}}, "is-nil": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-nil/-/is-nil-1.0.1.tgz", "integrity": "sha1-LauingtYUGOHXntTnQcfWxWTeWk="}, "is-number": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-3.0.0.tgz", "integrity": "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=", "requires": {"kind-of": "^3.0.2"}, "dependencies": {"is-buffer": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w=="}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "is-plain-object": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/is-plain-object/-/is-plain-object-2.0.4.tgz", "integrity": "sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==", "requires": {"isobject": "^3.0.1"}}, "is-regex": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/is-regex/-/is-regex-1.1.1.tgz", "integrity": "sha512-1+QkEcxiLlB7VEyFtyBg94e08OAsvq7FUBgApTq/w2ymCLyKJgDPsybBENVtA7XCQEgEXxKPonG+mvYRxh/LIg==", "requires": {"has-symbols": "^1.0.1"}}, "is-secret": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/is-secret/-/is-secret-1.2.1.tgz", "integrity": "sha512-VtBantcgKL2a64fDeCmD1JlkHToh3v0bVOhyJZ5aGTjxtCgrdNcjaC9GaaRFXi19gA4/pYFpnuyoscIgQCFSMQ=="}, "is-stream": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/is-stream/-/is-stream-1.1.0.tgz", "integrity": "sha1-EtSj3U5o4Lec6428hBc66A2RykQ="}, "is-symbol": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/is-symbol/-/is-symbol-1.0.3.tgz", "integrity": "sha512-OwijhaRSgqvhm/0ZdAcXNZt9lYdKFpcRDT5ULUuYXPoT794UNOdU+gpT6Rzo7b4V2HUl/op6GqY894AZwv9faQ==", "requires": {"has-symbols": "^1.0.1"}}, "is-typedarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz", "integrity": "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo="}, "is-windows": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-windows/-/is-windows-1.0.2.tgz", "integrity": "sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA=="}, "isarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="}, "isemail": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/isemail/-/isemail-1.2.0.tgz", "integrity": "sha1-vgPfjMPineTSxd9lASY/H6RZXpo="}, "isobject": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz", "integrity": "sha1-TkMekrEalzFjaqH5yNHMvP2reN8="}, "isstream": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/isstream/-/isstream-0.1.2.tgz", "integrity": "sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo="}, "javascript-natural-sort": {"version": "0.7.1", "resolved": "https://registry.npmjs.org/javascript-natural-sort/-/javascript-natural-sort-0.7.1.tgz", "integrity": "sha1-+eIwPUUH9tdDVac2ZNFED7Wg71k="}, "jmespath": {"version": "0.15.0", "resolved": "https://registry.npmjs.org/jmespath/-/jmespath-0.15.0.tgz", "integrity": "sha1-o/Iiqarp+Wb10nx5ZRDigJF2Qhc="}, "joi": {"version": "6.10.1", "resolved": "https://registry.npmjs.org/joi/-/joi-6.10.1.tgz", "integrity": "sha1-TVDDGAeRIgAP5fFq8f+OGRe3fgY=", "requires": {"hoek": "2.x.x", "isemail": "1.x.x", "moment": "2.x.x", "topo": "1.x.x"}, "dependencies": {"hoek": {"version": "2.16.3", "resolved": "https://registry.npmjs.org/hoek/-/hoek-2.16.3.tgz", "integrity": "sha1-ILt0A9POo5jpHcRxCo/xuCdKJe0="}}}, "js-string-escape": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/js-string-escape/-/js-string-escape-1.0.1.tgz", "integrity": "sha1-4mJbrbwNZ8dTPp7cEGjFh65BN+8="}, "js-tokens": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="}, "js-yaml": {"version": "3.14.0", "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.0.tgz", "integrity": "sha512-/4IbIeHcD9VMHFqDR/gQ7EdZdLimOvW2DdcxFjdyyZ9NsbS+ccrXqVWDtab/lRl5AlUqmpBx8EhPaWR+OtY17A==", "requires": {"argparse": "^1.0.7", "esprima": "^4.0.0"}}, "jsbn": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/jsbn/-/jsbn-1.1.0.tgz", "integrity": "sha1-sBMHyym2GKHtJux56RH4A8TaAEA="}, "json-parse-even-better-errors": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.0.tgz", "integrity": "sha512-o3aP+RsWDJZayj1SbHNQAI8x0v3T3SKiGoZlNYfbUP1S3omJQ6i9CnqADqkSPaOAxwua4/1YWx5CM7oiChJt2Q=="}, "json-refs": {"version": "2.1.7", "resolved": "https://registry.npmjs.org/json-refs/-/json-refs-2.1.7.tgz", "integrity": "sha1-uesB/in16j6Sh48VrqEK04taz4k=", "requires": {"commander": "^2.9.0", "graphlib": "^2.1.1", "js-yaml": "^3.8.3", "native-promise-only": "^0.8.1", "path-loader": "^1.0.2", "slash": "^1.0.0", "uri-js": "^3.0.2"}, "dependencies": {"punycode": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/punycode/-/punycode-2.1.1.tgz", "integrity": "sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A=="}, "uri-js": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/uri-js/-/uri-js-3.0.2.tgz", "integrity": "sha1-+QuFhQf4HepNz7s8TD2/orVX+qo=", "requires": {"punycode": "^2.1.0"}}}}, "json-schema": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/json-schema/-/json-schema-0.2.3.tgz", "integrity": "sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM="}, "json-schema-traverse": {"version": "0.4.1", "resolved": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg=="}, "json-stringify-safe": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "integrity": "sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus="}, "json2csv": {"version": "4.5.4", "resolved": "https://registry.npmjs.org/json2csv/-/json2csv-4.5.4.tgz", "integrity": "sha512-YxBhY4Lmn8IvVZ36nqg5omxneLy9JlorkqW1j/EDCeqvmi+CQ4uM+wsvXlcIqvGDewIPXMC/O/oF8DX9EH5aoA==", "requires": {"commander": "^2.15.1", "jsonparse": "^1.3.1", "lodash.get": "^4.4.2"}}, "json3": {"version": "3.3.2", "resolved": "https://registry.npmjs.org/json3/-/json3-3.3.2.tgz", "integrity": "sha1-PAQ0dD35Pi9cQq7nsZvLSDV19OE=", "dev": true}, "json5": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/json5/-/json5-1.0.1.tgz", "integrity": "sha512-aKS4WQjPenRxiQsC93MNfjx+nbF4PAdYzmd/1JIj8HYzqfbu86beTuNgXDzPknWk0n0uARlyewZo4s++ES36Ow==", "requires": {"minimist": "^1.2.0"}}, "jsonparse": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/jsonparse/-/jsonparse-1.3.1.tgz", "integrity": "sha1-P02uSpH6wxX3EGL4UhzCOfE2YoA="}, "jsonwebtoken": {"version": "7.4.3", "resolved": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-7.4.3.tgz", "integrity": "sha1-d/UCHeBYtgWheD+hKD6ZgS5kVjg=", "requires": {"joi": "^6.10.1", "jws": "^3.1.4", "lodash.once": "^4.0.0", "ms": "^2.0.0", "xtend": "^4.0.1"}}, "jspath": {"version": "0.3.4", "resolved": "https://registry.npmjs.org/jspath/-/jspath-0.3.4.tgz", "integrity": "sha1-2J0+0uh0NP5s0ASyQskS35aXNSQ="}, "jsprim": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/jsprim/-/jsprim-1.4.1.tgz", "integrity": "sha1-MT5mvB5cwG5Di8G3SZwuXFastqI=", "requires": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.2.3", "verror": "1.10.0"}}, "jwa": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/jwa/-/jwa-1.4.1.tgz", "integrity": "sha512-qiLX/xhEEFKUAJ6FiBMbes3w9ATzyk5W7Hvzpa/SLYdxNtng+gcurvrI7TbACjIXlsJyr05/S1oUhZrc63evQA==", "requires": {"buffer-equal-constant-time": "1.0.1", "ecdsa-sig-formatter": "1.0.11", "safe-buffer": "^5.0.1"}}, "jws": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/jws/-/jws-3.2.2.tgz", "integrity": "sha512-YHlZCB6lMTllWDtSPHz/ZXTsi8S00usEV6v1tjq8tOUZzw7DpSDWVXjXDre6ed1w/pd495ODpHZYSdkRTsa0HA==", "requires": {"jwa": "^1.4.1", "safe-buffer": "^5.0.1"}}, "kind-of": {"version": "6.0.3", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-6.0.3.tgz", "integrity": "sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw=="}, "kuler": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/kuler/-/kuler-1.0.1.tgz", "integrity": "sha512-J9nVUucG1p/skKul6DU3PUZrhs0LPulNaeUOox0IyXDi8S4CztTHs1gQphhuZmzXG7VOQSf6NJfKuzteQLv9gQ==", "requires": {"colornames": "^1.1.1"}}, "lazy": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/lazy/-/lazy-1.0.11.tgz", "integrity": "sha1-2qBoIGKCVCwIgojpdcKXwa53tpA="}, "lines-and-columns": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.1.6.tgz", "integrity": "sha1-HADHQ7QzzQpOgHWPe2SldEDZ/wA="}, "load-source-map": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/load-source-map/-/load-source-map-1.0.0.tgz", "integrity": "sha1-MY9JkFzopwnft8w/FvPv47zx3QU=", "requires": {"in-publish": "^2.0.0", "semver": "^5.3.0", "source-map": "^0.5.6"}, "dependencies": {"semver": {"version": "5.7.1", "resolved": "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz", "integrity": "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ=="}}}, "locate-path": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz", "integrity": "sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==", "requires": {"p-locate": "^4.1.0"}}, "lodash": {"version": "4.17.5", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.5.tgz", "integrity": "sha512-svL3uiZf1RwhH+cWrfZn3A4+U58wbP0tGVTLQPbjplZxZ8ROD9VLuNgsRniTlLe7OlSqR79RUehXgpBW/s0IQw=="}, "lodash-compat": {"version": "3.10.2", "resolved": "https://registry.npmjs.org/lodash-compat/-/lodash-compat-3.10.2.tgz", "integrity": "sha1-xpQBKKnTD46QLNLPmf0Muk7PwYM="}, "lodash._arraypool": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._arraypool/-/lodash._arraypool-2.4.1.tgz", "integrity": "sha1-6I7suS4ruEyQZWEv2VigcZzUf5Q="}, "lodash._baseassign": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/lodash._baseassign/-/lodash._baseassign-3.2.0.tgz", "integrity": "sha1-jDigmVAPIVrQnlnxci/QxSv+Ck4=", "dev": true, "requires": {"lodash._basecopy": "^3.0.0", "lodash.keys": "^3.0.0"}, "dependencies": {"lodash.isarray": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/lodash.isarray/-/lodash.isarray-3.0.4.tgz", "integrity": "sha1-eeTriMNqgSKvhvhEqpvNhRtfu1U=", "dev": true}, "lodash.keys": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/lodash.keys/-/lodash.keys-3.1.2.tgz", "integrity": "sha1-TbwEcrFWvlCgsoaFXRvQsMZWCYo=", "dev": true, "requires": {"lodash._getnative": "^3.0.0", "lodash.isarguments": "^3.0.0", "lodash.isarray": "^3.0.0"}}}}, "lodash._basebind": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._basebind/-/lodash._basebind-2.4.1.tgz", "integrity": "sha1-6UC5690nwyfgqNqxtVkWxTQelXU=", "requires": {"lodash._basecreate": "~2.4.1", "lodash._setbinddata": "~2.4.1", "lodash._slice": "~2.4.1", "lodash.isobject": "~2.4.1"}}, "lodash._baseclone": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._baseclone/-/lodash._baseclone-2.4.1.tgz", "integrity": "sha1-MPgj5X4X43NdODvWK2Czh1Q7QYY=", "requires": {"lodash._getarray": "~2.4.1", "lodash._releasearray": "~2.4.1", "lodash._slice": "~2.4.1", "lodash.assign": "~2.4.1", "lodash.foreach": "~2.4.1", "lodash.forown": "~2.4.1", "lodash.isarray": "~2.4.1", "lodash.isobject": "~2.4.1"}}, "lodash._basecopy": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/lodash._basecopy/-/lodash._basecopy-3.0.1.tgz", "integrity": "sha1-jaDmqHbPNEwK2KVIghEd08XHyjY=", "dev": true}, "lodash._basecreate": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._basecreate/-/lodash._basecreate-2.4.1.tgz", "integrity": "sha1-+Ob1tXip405UEXm1a47uv0oofgg=", "requires": {"lodash._isnative": "~2.4.1", "lodash.isobject": "~2.4.1", "lodash.noop": "~2.4.1"}}, "lodash._basecreatecallback": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._basecreatecallback/-/lodash._basecreatecallback-2.4.1.tgz", "integrity": "sha1-fQsmdknLKeehOdAQO3wR+uhOSFE=", "requires": {"lodash._setbinddata": "~2.4.1", "lodash.bind": "~2.4.1", "lodash.identity": "~2.4.1", "lodash.support": "~2.4.1"}}, "lodash._basecreatewrapper": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._basecreatewrapper/-/lodash._basecreatewrapper-2.4.1.tgz", "integrity": "sha1-TTHy595+E0+/KAN2K4FQsyUZZm8=", "requires": {"lodash._basecreate": "~2.4.1", "lodash._setbinddata": "~2.4.1", "lodash._slice": "~2.4.1", "lodash.isobject": "~2.4.1"}}, "lodash._createwrapper": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._createwrapper/-/lodash._createwrapper-2.4.1.tgz", "integrity": "sha1-UdaVeXPaTtVW43KQ2MGhjFPeFgc=", "requires": {"lodash._basebind": "~2.4.1", "lodash._basecreatewrapper": "~2.4.1", "lodash._slice": "~2.4.1", "lodash.isfunction": "~2.4.1"}}, "lodash._getarray": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._getarray/-/lodash._getarray-2.4.1.tgz", "integrity": "sha1-+vH3+BD6mFolHCGHQESBCUg55e4=", "requires": {"lodash._arraypool": "~2.4.1"}}, "lodash._getnative": {"version": "3.9.1", "resolved": "https://registry.npmjs.org/lodash._getnative/-/lodash._getnative-3.9.1.tgz", "integrity": "sha1-VwvH3t5G1hzc3mh9ZdPuy6o6r/U=", "dev": true}, "lodash._isiterateecall": {"version": "3.0.9", "resolved": "https://registry.npmjs.org/lodash._isiterateecall/-/lodash._isiterateecall-3.0.9.tgz", "integrity": "sha1-UgOte6Ql+uhCRg5pbbnPPmqsBXw=", "dev": true}, "lodash._isnative": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._isnative/-/lodash._isnative-2.4.1.tgz", "integrity": "sha1-PqZAS3hKe+g2x7V1gOHN95sUgyw="}, "lodash._maxpoolsize": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._maxpoolsize/-/lodash._maxpoolsize-2.4.1.tgz", "integrity": "sha1-nUgvRjuOZq++WcLBTtsRcGAXIzQ="}, "lodash._objecttypes": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._objecttypes/-/lodash._objecttypes-2.4.1.tgz", "integrity": "sha1-fAt/admKH3ZSn4kLDNsbTf7BHBE="}, "lodash._releasearray": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._releasearray/-/lodash._releasearray-2.4.1.tgz", "integrity": "sha1-phOWMNdtFTawfdyAliiJsIL2pkE=", "requires": {"lodash._arraypool": "~2.4.1", "lodash._maxpoolsize": "~2.4.1"}}, "lodash._setbinddata": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._setbinddata/-/lodash._setbinddata-2.4.1.tgz", "integrity": "sha1-98IAzRuS7yNrOZ7s9zxkjReqlNI=", "requires": {"lodash._isnative": "~2.4.1", "lodash.noop": "~2.4.1"}}, "lodash._shimkeys": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._shimkeys/-/lodash._shimkeys-2.4.1.tgz", "integrity": "sha1-bpzJZm/wgfC1psl4uD4kLmlJ0gM=", "requires": {"lodash._objecttypes": "~2.4.1"}}, "lodash._slice": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._slice/-/lodash._slice-2.4.1.tgz", "integrity": "sha1-dFz0GlNZexj2iImFREBe+isG2Q8="}, "lodash.assign": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.assign/-/lodash.assign-2.4.1.tgz", "integrity": "sha1-hMOVlt1xGBqXsGUpE6fJZ15Jsao=", "requires": {"lodash._basecreatecallback": "~2.4.1", "lodash._objecttypes": "~2.4.1", "lodash.keys": "~2.4.1"}}, "lodash.bind": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.bind/-/lodash.bind-2.4.1.tgz", "integrity": "sha1-XRn6AFyMTSNvr0dCx7eh/Kvikmc=", "requires": {"lodash._createwrapper": "~2.4.1", "lodash._slice": "~2.4.1"}}, "lodash.clonedeep": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.clonedeep/-/lodash.clonedeep-2.4.1.tgz", "integrity": "sha1-8pIDtAsS/uCkXTYxZIJZvrq8eGg=", "requires": {"lodash._baseclone": "~2.4.1", "lodash._basecreatecallback": "~2.4.1"}}, "lodash.create": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/lodash.create/-/lodash.create-3.1.1.tgz", "integrity": "sha1-1/KEnw29p+BGgruM1yqwIkYd6+c=", "dev": true, "requires": {"lodash._baseassign": "^3.0.0", "lodash._basecreate": "^3.0.0", "lodash._isiterateecall": "^3.0.0"}, "dependencies": {"lodash._basecreate": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/lodash._basecreate/-/lodash._basecreate-3.0.3.tgz", "integrity": "sha1-G8ZhYU2qf8MRt9A78WgGoCE8+CE=", "dev": true}}}, "lodash.defaultsdeep": {"version": "4.6.1", "resolved": "https://registry.npmjs.org/lodash.defaultsdeep/-/lodash.defaultsdeep-4.6.1.tgz", "integrity": "sha512-3j8wdDzYuWO3lM3Reg03MuQR957t287Rpcxp1njpEa8oDrikb+FwGdW3n+FELh/A6qib6yPit0j/pv9G/yeAqA=="}, "lodash.findindex": {"version": "4.6.0", "resolved": "https://registry.npmjs.org/lodash.findindex/-/lodash.findindex-4.6.0.tgz", "integrity": "sha1-oyRd7mH7m24GJLU1ElYku2nBEQY="}, "lodash.foreach": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.foreach/-/lodash.foreach-2.4.1.tgz", "integrity": "sha1-/j/Do0yGyUyrb5UiVgKCdB4BYwk=", "requires": {"lodash._basecreatecallback": "~2.4.1", "lodash.forown": "~2.4.1"}}, "lodash.forown": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.forown/-/lodash.forown-2.4.1.tgz", "integrity": "sha1-eLQer+FAX6lmRZ6kGT/VAtCEUks=", "requires": {"lodash._basecreatecallback": "~2.4.1", "lodash._objecttypes": "~2.4.1", "lodash.keys": "~2.4.1"}}, "lodash.get": {"version": "4.4.2", "resolved": "https://registry.npmjs.org/lodash.get/-/lodash.get-4.4.2.tgz", "integrity": "sha1-LRd/ZS+jHpObRDjVNBSZ36OCXpk="}, "lodash.identity": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.identity/-/lodash.identity-2.4.1.tgz", "integrity": "sha1-ZpTP+mX++TH3wxzobHRZfPVg9PE="}, "lodash.includes": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/lodash.includes/-/lodash.includes-4.3.0.tgz", "integrity": "sha1-YLuYqHy5I8aMoeUTJUgzFISfVT8="}, "lodash.isarguments": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/lodash.isarguments/-/lodash.isarguments-3.1.0.tgz", "integrity": "sha1-L1c9hcaiQon/AGY7SRwdM4/zRYo=", "dev": true}, "lodash.isarray": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.isarray/-/lodash.isarray-2.4.1.tgz", "integrity": "sha1-tSoybB9i9tfac6MdVAHfbvRPD6E=", "requires": {"lodash._isnative": "~2.4.1"}}, "lodash.isboolean": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/lodash.isboolean/-/lodash.isboolean-3.0.3.tgz", "integrity": "sha1-bC4XHbKiV82WgC/UOwGyDV9YcPY="}, "lodash.isequal": {"version": "4.5.0", "resolved": "https://registry.npmjs.org/lodash.isequal/-/lodash.isequal-4.5.0.tgz", "integrity": "sha1-QVxEePK8wwEgwizhDtMib30+GOA="}, "lodash.isfunction": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.isfunction/-/lodash.isfunction-2.4.1.tgz", "integrity": "sha1-LP1XXHPkmKtX4xm3f6Aq3vE6lNE="}, "lodash.isinteger": {"version": "4.0.4", "resolved": "https://registry.npmjs.org/lodash.isinteger/-/lodash.isinteger-4.0.4.tgz", "integrity": "sha1-YZwK89A/iwTDH1iChAt3sRzWg0M="}, "lodash.isnumber": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/lodash.isnumber/-/lodash.isnumber-3.0.3.tgz", "integrity": "sha1-POdoEMWSjQM1IwGsKHMX8RwLH/w="}, "lodash.isobject": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.isobject/-/lodash.isobject-2.4.1.tgz", "integrity": "sha1-Wi5H/mmVPx7mMafrof5k0tBlWPU=", "requires": {"lodash._objecttypes": "~2.4.1"}}, "lodash.isplainobject": {"version": "4.0.6", "resolved": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz", "integrity": "sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs="}, "lodash.isstring": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/lodash.isstring/-/lodash.isstring-4.0.1.tgz", "integrity": "sha1-1SfftUVuynzJu5XV2ur4i6VKVFE="}, "lodash.keys": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.keys/-/lodash.keys-2.4.1.tgz", "integrity": "sha1-SN6kbfj/djKxDXBrissmWR4rNyc=", "requires": {"lodash._isnative": "~2.4.1", "lodash._shimkeys": "~2.4.1", "lodash.isobject": "~2.4.1"}}, "lodash.merge": {"version": "4.6.2", "resolved": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz", "integrity": "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ=="}, "lodash.noop": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.noop/-/lodash.noop-2.4.1.tgz", "integrity": "sha1-T7VPgWZS5a4Q6PcvcXo4jHMmU4o="}, "lodash.once": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/lodash.once/-/lodash.once-4.1.1.tgz", "integrity": "sha1-DdOXEhPHxW34gJd9UEyI+0cal6w="}, "lodash.reduce": {"version": "4.6.0", "resolved": "https://registry.npmjs.org/lodash.reduce/-/lodash.reduce-4.6.0.tgz", "integrity": "sha1-8atrg5KZrUj3hKu/R2WW8DuRTTs="}, "lodash.support": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.support/-/lodash.support-2.4.1.tgz", "integrity": "sha1-Mg4LZwMWc8KNeiu12eAzGkUkBRU=", "requires": {"lodash._isnative": "~2.4.1"}}, "logform": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/logform/-/logform-2.2.0.tgz", "integrity": "sha512-N0qPlqfypFx7UHNn4B3lzS/b0uLqt2hmuoa+PpuXNYgozdJYAyauF5Ky0BWVjrxDlMWiT3qN4zPq3vVAfZy7Yg==", "requires": {"colors": "^1.2.1", "fast-safe-stringify": "^2.0.4", "fecha": "^4.2.0", "ms": "^2.1.1", "triple-beam": "^1.3.0"}}, "loose-envify": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz", "integrity": "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==", "requires": {"js-tokens": "^3.0.0 || ^4.0.0"}}, "lru-cache": {"version": "4.1.5", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-4.1.5.tgz", "integrity": "sha512-sWZlbEP2OsHNkXrMl5GYk/jKk70MBng6UU4YI/qGDYbgf6YbP4EvmqISbXCoJiRKs+1bSpFHVgQxvJ17F2li5g==", "requires": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "machine": {"version": "10.4.0", "resolved": "https://registry.npmjs.org/machine/-/machine-10.4.0.tgz", "integrity": "sha1-m1Ys5GeCEzKCijd9GQ65NrTkB7I=", "requires": {"convert-to-ecmascript-compatible-varname": "^0.1.0", "debug": "^2.1.1", "lodash": "^3.8.0", "object-hash": "~0.3.0", "rttc": "^7.2.1", "switchback": "^2.0.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "lodash": {"version": "3.10.1", "resolved": "https://registry.npmjs.org/lodash/-/lodash-3.10.1.tgz", "integrity": "sha1-W/Rejkm6QYnhfUgnid/RW9FAt7Y="}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "machinepack-http": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/machinepack-http/-/machinepack-http-2.4.0.tgz", "integrity": "sha1-CnhcF9xrnBuaxAiBvu+uiudIVek=", "requires": {"lodash": "^3.9.2", "machine": "^10.3.1", "machinepack-urls": "^4.0.0", "request": "^2.55.0"}, "dependencies": {"lodash": {"version": "3.10.1", "resolved": "https://registry.npmjs.org/lodash/-/lodash-3.10.1.tgz", "integrity": "sha1-W/Rejkm6QYnhfUgnid/RW9FAt7Y="}}}, "machinepack-urls": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/machinepack-urls/-/machinepack-urls-4.1.0.tgz", "integrity": "sha1-0l4y6Xw8LLiVaLqMmNIp1cMF45E=", "requires": {"lodash": "^3.9.2", "machine": "^9.0.3"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "lodash": {"version": "3.10.1", "resolved": "https://registry.npmjs.org/lodash/-/lodash-3.10.1.tgz", "integrity": "sha1-W/Rejkm6QYnhfUgnid/RW9FAt7Y="}, "machine": {"version": "9.1.2", "resolved": "https://registry.npmjs.org/machine/-/machine-9.1.2.tgz", "integrity": "sha1-hL+Pt3ZqlqplqpbWbpUJ62oFqDQ=", "requires": {"convert-to-ecmascript-compatible-varname": "^0.1.0", "debug": "^2.1.1", "lodash": "^3.8.0", "object-hash": "~0.3.0", "rttc": "^4.0.0", "switchback": "^1.1.3"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "rttc": {"version": "4.5.2", "resolved": "https://registry.npmjs.org/rttc/-/rttc-4.5.2.tgz", "integrity": "sha1-umo+komLQnTxI7usSUhddhajfLw=", "requires": {"lodash": "^3.8.0"}}, "switchback": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/switchback/-/switchback-1.1.3.tgz", "integrity": "sha1-EscBCTSNailvc5upEO64U/i25jE=", "requires": {"lodash": "~2.4.1"}, "dependencies": {"lodash": {"version": "2.4.2", "resolved": "https://registry.npmjs.org/lodash/-/lodash-2.4.2.tgz", "integrity": "sha1-+t2DS5aDBz2hebPq5tnA0VBT9z4="}}}}}, "map-cache": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/map-cache/-/map-cache-0.2.2.tgz", "integrity": "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8="}, "map-visit": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/map-visit/-/map-visit-1.0.0.tgz", "integrity": "sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=", "requires": {"object-visit": "^1.0.0"}}, "mapcap": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/mapcap/-/mapcap-1.0.0.tgz", "integrity": "sha512-KcNlZSlFPx+r1jYZmxEbTVymG+dIctf10WmWkuhrhrblM+KMoF77HelwihL5cxYlORye79KoR4IlOOk99lUJ0g=="}, "mathjs": {"version": "3.20.2", "resolved": "https://registry.npmjs.org/mathjs/-/mathjs-3.20.2.tgz", "integrity": "sha512-3f6/+uf1cUtIz1rYFz775wekl/UEDSQ3mU6xdxW7qzpvvhc2v28i3UtLsGTRB+u8OqDWoSX6Dz8gehaGFs6tCA==", "requires": {"complex.js": "2.0.4", "decimal.js": "9.0.1", "escape-latex": "^1.0.0", "fraction.js": "4.0.4", "javascript-natural-sort": "0.7.1", "seed-random": "2.2.0", "tiny-emitter": "2.0.2", "typed-function": "0.10.7"}}, "measured-core": {"version": "1.51.1", "resolved": "https://registry.npmjs.org/measured-core/-/measured-core-1.51.1.tgz", "integrity": "sha512-DZQP9SEwdqqYRvT2slMK81D/7xwdxXosZZBtLVfPSo6y5P672FBTbzHVdN4IQyUkUpcVOR9pIvtUy5Ryl7NKyg==", "requires": {"binary-search": "^1.3.3", "optional-js": "^2.0.0"}}, "measured-reporting": {"version": "1.51.1", "resolved": "https://registry.npmjs.org/measured-reporting/-/measured-reporting-1.51.1.tgz", "integrity": "sha512-JCt+2u6XT1I5lG3SuYqywE0e62DJuAzBcfMzWGUhIYtPQV2Vm4HiYt/durqmzsAbZV181CEs+o/jMKWJKkYIWw==", "requires": {"console-log-level": "^1.4.1", "mapcap": "^1.0.0", "measured-core": "^1.51.1", "optional-js": "^2.0.0"}}, "media-typer": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g="}, "merge-descriptors": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.1.tgz", "integrity": "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E="}, "methods": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4="}, "micromatch": {"version": "3.1.10", "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.10.tgz", "integrity": "sha512-MWikgl9n9M3w+bpsY3He8L+w9eF9338xRl8IAO5viDizwSzziFEyUzo2xrrloB64ADbTf8uA8vRqqttDTOmccg==", "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}}, "mime": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/mime/-/mime-1.4.1.tgz", "integrity": "sha512-KI1+qOZu5DcW6wayYHSzR/tXKCDC5Om4s1z2QJjDULzLcmf3DvzS7oluY4HCTrc+9FiKmWUgeNLg7W3uIQvxtQ=="}, "mime-db": {"version": "1.44.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.44.0.tgz", "integrity": "sha512-/NOTfLrsPBVeH7YtFPgsVWveuL+4SjjYxaQ1xtM1KMFj7HdxlBlxeyNLzhyJVx7r4rZGJAZ/6lkKCitSc/Nmpg=="}, "mime-types": {"version": "2.1.27", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.27.tgz", "integrity": "sha512-JIhqnCasI9yD+SsmkquHBxTSEuZdQX5BuQnS2Vc7puQQQ+8yiP5AY5uWhpdv4YL4VM5c6iliiYWPgJ/nJQLp7w==", "requires": {"mime-db": "1.44.0"}}, "minimatch": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz", "integrity": "sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==", "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "1.2.5", "resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.5.tgz", "integrity": "sha512-FM9nNUYrRBAELZQT3xeZQ7fmMOBg6nWNmJKTcgsJeaLstP/UODVpGsr5OhXhhXg6f+qtJ8uiZ+PUxkDWcgIXLw=="}, "mixin-deep": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/mixin-deep/-/mixin-deep-1.3.2.tgz", "integrity": "sha512-WRoDn//mXBiJ1H40rqa3vH0toePwSsGb45iInWlTySa+Uu4k3tYUSxa2v1KqAiLtvlrSzaExqS1gtk96A9zvEA==", "requires": {"for-in": "^1.0.2", "is-extendable": "^1.0.1"}, "dependencies": {"is-extendable": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-extendable/-/is-extendable-1.0.1.tgz", "integrity": "sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA==", "requires": {"is-plain-object": "^2.0.4"}}}}, "mkdirp": {"version": "0.5.5", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.5.tgz", "integrity": "sha512-NKmAlESf6jMGym1++R0Ra7wvhV+wFW63FaSOFPwRahvea0gMUcGUhVeAg/0BC0wiv9ih5NYPB1Wn1UEI1/L+xQ==", "requires": {"minimist": "^1.2.5"}}, "mocha": {"version": "3.5.3", "resolved": "https://registry.npmjs.org/mocha/-/mocha-3.5.3.tgz", "integrity": "sha512-/6na001MJWEtYxHOV1WLfsmR4YIynkUEhBwzsb+fk2qmQ3iqsi258l/Q2MWHJMImAcNpZ8DEdYAK72NHoIQ9Eg==", "dev": true, "requires": {"browser-stdout": "1.3.0", "commander": "2.9.0", "debug": "2.6.8", "diff": "3.2.0", "escape-string-regexp": "1.0.5", "glob": "7.1.1", "growl": "1.9.2", "he": "1.1.1", "json3": "3.3.2", "lodash.create": "3.1.1", "mkdirp": "0.5.1", "supports-color": "3.1.2"}, "dependencies": {"commander": {"version": "2.9.0", "resolved": "https://registry.npmjs.org/commander/-/commander-2.9.0.tgz", "integrity": "sha1-nJkJQXbhIkDLItbFFGCYQA/g99Q=", "dev": true, "requires": {"graceful-readlink": ">= 1.0.0"}}, "debug": {"version": "2.6.8", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.8.tgz", "integrity": "sha1-5zFTHKLt4n0YgiJCfaF4IdaP9Pw=", "dev": true, "requires": {"ms": "2.0.0"}}, "glob": {"version": "7.1.1", "resolved": "https://registry.npmjs.org/glob/-/glob-7.1.1.tgz", "integrity": "sha1-gFIR3wT6rxxjo2ADBs31reULLsg=", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.2", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "has-flag": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-1.0.0.tgz", "integrity": "sha1-nZ55MWXOAXoA8AQYxD+UKnsdEfo=", "dev": true}, "minimist": {"version": "0.0.8", "resolved": "https://registry.npmjs.org/minimist/-/minimist-0.0.8.tgz", "integrity": "sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0=", "dev": true}, "mkdirp": {"version": "0.5.1", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.1.tgz", "integrity": "sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM=", "dev": true, "requires": {"minimist": "0.0.8"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}, "supports-color": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-3.1.2.tgz", "integrity": "sha1-cqJiiU2dQIuVbKBf83su2KbiotU=", "dev": true, "requires": {"has-flag": "^1.0.0"}}}}, "module-details-from-path": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/module-details-from-path/-/module-details-from-path-1.0.3.tgz", "integrity": "sha1-EUyUlnPiqKNenTV4hSeqN7Z52is="}, "moment": {"version": "2.21.0", "resolved": "https://registry.npmjs.org/moment/-/moment-2.21.0.tgz", "integrity": "sha512-TCZ36BjURTeFTM/CwRcViQlfkMvL1/vFISuNLO5GkcVm1+QHfbSiNqZuWeMFjj1/3+uAjXswgRk30j1kkLYJBQ=="}, "moment-timezone": {"version": "0.5.28", "resolved": "https://registry.npmjs.org/moment-timezone/-/moment-timezone-0.5.28.tgz", "integrity": "sha512-TDJkZvAyKIVWg5EtVqRzU97w0Rb0YVbfpqyjgu6GwXCAohVRqwZjf4fOzDE6p1Ch98Sro/8hQQi65WDXW5STPw==", "requires": {"moment": ">= 2.9.0"}}, "monitor-event-loop-delay": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/monitor-event-loop-delay/-/monitor-event-loop-delay-1.0.0.tgz", "integrity": "sha512-YRIr1exCIfBDLZle8WHOfSo7Xg3M+phcZfq9Fx1L6Abo+atGp7cge5pM7PjyBn4s1oZI/BRD4EMrzQBbPpVb5Q=="}, "morgan": {"version": "1.9.0", "resolved": "https://registry.npmjs.org/morgan/-/morgan-1.9.0.tgz", "integrity": "sha1-0B+mxlhZt2/PMbPLU6OCGjEdgFE=", "requires": {"basic-auth": "~2.0.0", "debug": "2.6.9", "depd": "~1.1.1", "on-finished": "~2.3.0", "on-headers": "~1.0.1"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "ms": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="}, "multer": {"version": "1.4.2", "resolved": "https://registry.npmjs.org/multer/-/multer-1.4.2.tgz", "integrity": "sha512-xY8pX7V+ybyUpbYMxtjM9KAiD9ixtg5/JkeKUTD6xilfDv0vzzOFcCp4Ljb1UU3tSOM3VTZtKo63OmzOrGi3Cg==", "requires": {"append-field": "^1.0.0", "busboy": "^0.2.11", "concat-stream": "^1.5.2", "mkdirp": "^0.5.1", "object-assign": "^4.1.1", "on-finished": "^2.3.0", "type-is": "^1.6.4", "xtend": "^4.0.0"}}, "mustache": {"version": "2.3.2", "resolved": "https://registry.npmjs.org/mustache/-/mustache-2.3.2.tgz", "integrity": "sha512-KpMNwdQsYz3O/SBS1qJ/o3sqUJ5wSb8gb0pul8CO0S56b9Y2ALm8zCfsjPXsqGFfoNBkDwZuZIAjhsZI03gYVQ=="}, "mute-stream": {"version": "0.0.8", "resolved": "https://registry.npmjs.org/mute-stream/-/mute-stream-0.0.8.tgz", "integrity": "sha512-nnbWWOkoWyUsTjKrhgD0dcz22mdkSnpYqbEjIm2nhwhuxlSkpywJmBo8h0ZqJdkp73mb90SssHkN4rsRaBAfAA=="}, "nan": {"version": "2.14.1", "resolved": "https://registry.npmjs.org/nan/-/nan-2.14.1.tgz", "integrity": "sha512-isWHgVjnFjh2x2yuJ/tj3JbwoHu3UC2dX5G/88Cm24yB6YopVgxvBObDY7n5xW6ExmFhJpSEQqFPvq9zaXc8Jw==", "optional": true}, "nanomatch": {"version": "1.2.13", "resolved": "https://registry.npmjs.org/nanomatch/-/nanomatch-1.2.13.tgz", "integrity": "sha512-fpoe2T0RbHwBTBUOftAfBPaDEi06ufaUai0mE6Yn1kacc3SnTErfb/h+X94VXzI64rKFHYImXSvdwGGCmwOqCA==", "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "is-windows": "^1.0.2", "kind-of": "^6.0.2", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}}, "native-promise-only": {"version": "0.8.1", "resolved": "https://registry.npmjs.org/native-promise-only/-/native-promise-only-0.8.1.tgz", "integrity": "sha1-IKMYwwy0X3H+et+/eyHJnBRy7xE="}, "needle": {"version": "2.5.0", "resolved": "https://registry.npmjs.org/needle/-/needle-2.5.0.tgz", "integrity": "sha512-o/qITSDR0JCyCKEQ1/1bnUXMmznxabbwi/Y4WwJElf+evwJNFNwIDMCCt5IigFVxgeGBJESLohGtIS9gEzo1fA==", "requires": {"debug": "^3.2.6", "iconv-lite": "^0.4.4", "sax": "^1.2.4"}, "dependencies": {"debug": {"version": "3.2.6", "resolved": "https://registry.npmjs.org/debug/-/debug-3.2.6.tgz", "integrity": "sha512-mel+jf7nrtEl5Pn1Qx46zARXKDpBbvzezse7p7LqINmdoIk8PYP5SySaxEmYv6TZ0JyEKA1hsCId6DIhgITtWQ==", "requires": {"ms": "^2.1.1"}}, "sax": {"version": "1.2.4", "resolved": "https://registry.npmjs.org/sax/-/sax-1.2.4.tgz", "integrity": "sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw=="}}}, "negotiator": {"version": "0.6.2", "resolved": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.2.tgz", "integrity": "sha512-hZXc7K2e+PgeI1eDBe/10Ard4ekbfrrqG8Ep+8Jmf4JID2bNg7NvCPOZN+kfF574pFQI7mum2AUqDidoKqcTOw=="}, "next-line": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/next-line/-/next-line-1.1.0.tgz", "integrity": "sha1-/K5XhTBStqm66CCOQN19PC0wRgM="}, "nocache": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/nocache/-/nocache-2.0.0.tgz", "integrity": "sha1-ICtIAhoMTL3i34DeFaF0Q8i0OYA="}, "node-cron": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/node-cron/-/node-cron-2.0.3.tgz", "integrity": "sha512-eJI+QitXlwcgiZwNNSRbqsjeZMp5shyajMR81RZCqeW0ZDEj4zU9tpd4nTh/1JsBiKbF8d08FCewiipDmVIYjg==", "requires": {"opencollective-postinstall": "^2.0.0", "tz-offset": "0.0.1"}}, "nodemailer": {"version": "6.4.6", "resolved": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.4.6.tgz", "integrity": "sha512-/kJ+FYVEm2HuUlw87hjSqTss+GU35D4giOpdSfGp7DO+5h6RlJj7R94YaYHOkoxu1CSaM0d3WRBtCzwXrY6MKA=="}, "normalize-package-data": {"version": "2.5.0", "resolved": "https://registry.npmjs.org/normalize-package-data/-/normalize-package-data-2.5.0.tgz", "integrity": "sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA==", "requires": {"hosted-git-info": "^2.1.4", "resolve": "^1.10.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}, "dependencies": {"semver": {"version": "5.7.1", "resolved": "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz", "integrity": "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ=="}}}, "normalize-path": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz", "integrity": "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA=="}, "notepack.io": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/notepack.io/-/notepack.io-2.1.3.tgz", "integrity": "sha512-AgSt+cP5XMooho1Ppn8NB3FFaVWefV+qZoZncYTUSch2GAEwlYLcIIbT5YVkMlFeNHnfwOvc4HDlbvrB5BRxXA=="}, "npm": {"version": "5.7.1", "resolved": "https://registry.npmjs.org/npm/-/npm-5.7.1.tgz", "integrity": "sha512-r1grvv6mcEt+nlMzMWPc5n/z5q8NNuBWj0TGFp1PBSFCl6ubnAoUGBsucYsnZYT7MOJn0ha1ptEjmdBoAdJ+SA==", "requires": {"JSONStream": "^1.3.2", "abbrev": "~1.1.1", "ansi-regex": "~3.0.0", "ansicolors": "~0.3.2", "ansistyles": "~0.1.3", "aproba": "~1.2.0", "archy": "~1.0.0", "bin-links": "^1.1.0", "bluebird": "~3.5.1", "cacache": "^10.0.4", "call-limit": "~1.1.0", "chownr": "~1.0.1", "cli-table2": "~0.2.0", "cmd-shim": "~2.0.2", "columnify": "~1.5.4", "config-chain": "~1.1.11", "debuglog": "*", "detect-indent": "~5.0.0", "dezalgo": "~1.0.3", "editor": "~1.0.0", "find-npm-prefix": "^1.0.2", "fs-vacuum": "~1.2.10", "fs-write-stream-atomic": "~1.0.10", "gentle-fs": "^2.0.1", "glob": "~7.1.2", "graceful-fs": "~4.1.11", "has-unicode": "~2.0.1", "hosted-git-info": "~2.5.0", "iferr": "~0.1.5", "imurmurhash": "*", "inflight": "~1.0.6", "inherits": "~2.0.3", "ini": "^1.3.5", "init-package-json": "~1.10.1", "is-cidr": "~1.0.0", "lazy-property": "~1.0.0", "libcipm": "^1.3.3", "libnpx": "~9.7.1", "lockfile": "~1.0.3", "lodash._baseindexof": "*", "lodash._baseuniq": "~4.6.0", "lodash._bindcallback": "*", "lodash._cacheindexof": "*", "lodash._createcache": "*", "lodash._getnative": "*", "lodash.clonedeep": "~4.5.0", "lodash.restparam": "*", "lodash.union": "~4.6.0", "lodash.uniq": "~4.5.0", "lodash.without": "~4.4.0", "lru-cache": "~4.1.1", "meant": "~1.0.1", "mississippi": "^2.0.0", "mkdirp": "~0.5.1", "move-concurrently": "^1.0.1", "nopt": "~4.0.1", "normalize-package-data": "~2.4.0", "npm-cache-filename": "~1.0.2", "npm-install-checks": "~3.0.0", "npm-lifecycle": "~2.0.0", "npm-package-arg": "~6.0.0", "npm-packlist": "~1.1.10", "npm-profile": "^3.0.1", "npm-registry-client": "~8.5.0", "npm-user-validate": "~1.0.0", "npmlog": "~4.1.2", "once": "~1.4.0", "opener": "~1.4.3", "osenv": "^0.1.5", "pacote": "^7.3.3", "path-is-inside": "~1.0.2", "promise-inflight": "~1.0.1", "qrcode-terminal": "~0.11.0", "query-string": "^5.1.0", "qw": "~1.0.1", "read": "~1.0.7", "read-cmd-shim": "~1.0.1", "read-installed": "~4.0.3", "read-package-json": "~2.0.12", "read-package-tree": "~5.1.6", "readable-stream": "^2.3.4", "readdir-scoped-modules": "*", "request": "~2.83.0", "retry": "~0.10.1", "rimraf": "~2.6.2", "safe-buffer": "~5.1.1", "semver": "^5.5.0", "sha": "~2.0.1", "slide": "~1.1.6", "sorted-object": "~2.0.1", "sorted-union-stream": "~2.1.3", "ssri": "^5.2.4", "strip-ansi": "~4.0.0", "tar": "^4.3.3", "text-table": "~0.2.0", "uid-number": "0.0.6", "umask": "~1.1.0", "unique-filename": "~1.1.0", "unpipe": "~1.0.0", "update-notifier": "~2.3.0", "uuid": "^3.2.1", "validate-npm-package-license": "*", "validate-npm-package-name": "~3.0.0", "which": "~1.3.0", "worker-farm": "^1.5.2", "wrappy": "~1.0.2", "write-file-atomic": "~2.1.0"}, "dependencies": {"JSONStream": {"version": "1.3.2", "bundled": true, "requires": {"jsonparse": "^1.2.0", "through": ">=2.2.7 <3"}, "dependencies": {"jsonparse": {"version": "1.3.1", "bundled": true}, "through": {"version": "2.3.8", "bundled": true}}}, "abbrev": {"version": "1.1.1", "bundled": true}, "ansi-regex": {"version": "3.0.0", "bundled": true}, "ansicolors": {"version": "0.3.2", "bundled": true}, "ansistyles": {"version": "0.1.3", "bundled": true}, "aproba": {"version": "1.2.0", "bundled": true}, "archy": {"version": "1.0.0", "bundled": true}, "bin-links": {"version": "1.1.0", "bundled": true, "requires": {"bluebird": "^3.5.0", "cmd-shim": "^2.0.2", "fs-write-stream-atomic": "^1.0.10", "gentle-fs": "^2.0.0", "graceful-fs": "^4.1.11", "slide": "^1.1.6"}}, "bluebird": {"version": "3.5.1", "bundled": true}, "cacache": {"version": "10.0.4", "bundled": true, "requires": {"bluebird": "^3.5.1", "chownr": "^1.0.1", "glob": "^7.1.2", "graceful-fs": "^4.1.11", "lru-cache": "^4.1.1", "mississippi": "^2.0.0", "mkdirp": "^0.5.1", "move-concurrently": "^1.0.1", "promise-inflight": "^1.0.1", "rimraf": "^2.6.2", "ssri": "^5.2.4", "unique-filename": "^1.1.0", "y18n": "^4.0.0"}, "dependencies": {"y18n": {"version": "4.0.0", "bundled": true}}}, "call-limit": {"version": "1.1.0", "bundled": true}, "chownr": {"version": "1.0.1", "bundled": true}, "cli-table2": {"version": "0.2.0", "bundled": true, "requires": {"colors": "^1.1.2", "lodash": "^3.10.1", "string-width": "^1.0.1"}, "dependencies": {"colors": {"version": "1.1.2", "bundled": true, "optional": true}, "lodash": {"version": "3.10.1", "bundled": true}, "string-width": {"version": "1.0.2", "bundled": true, "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}, "dependencies": {"code-point-at": {"version": "1.1.0", "bundled": true}, "is-fullwidth-code-point": {"version": "1.0.0", "bundled": true, "requires": {"number-is-nan": "^1.0.0"}, "dependencies": {"number-is-nan": {"version": "1.0.1", "bundled": true}}}, "strip-ansi": {"version": "3.0.1", "bundled": true, "requires": {"ansi-regex": "^2.0.0"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "bundled": true}}}}}}}, "cmd-shim": {"version": "2.0.2", "bundled": true, "requires": {"graceful-fs": "^4.1.2", "mkdirp": "~0.5.0"}}, "columnify": {"version": "1.5.4", "bundled": true, "requires": {"strip-ansi": "^3.0.0", "wcwidth": "^1.0.0"}, "dependencies": {"strip-ansi": {"version": "3.0.1", "bundled": true, "requires": {"ansi-regex": "^2.0.0"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "bundled": true}}}, "wcwidth": {"version": "1.0.1", "bundled": true, "requires": {"defaults": "^1.0.3"}, "dependencies": {"defaults": {"version": "1.0.3", "bundled": true, "requires": {"clone": "^1.0.2"}, "dependencies": {"clone": {"version": "1.0.2", "bundled": true}}}}}}}, "config-chain": {"version": "1.1.11", "bundled": true, "requires": {"ini": "^1.3.4", "proto-list": "~1.2.1"}, "dependencies": {"proto-list": {"version": "1.2.4", "bundled": true}}}, "debuglog": {"version": "1.0.1", "bundled": true}, "detect-indent": {"version": "5.0.0", "bundled": true}, "dezalgo": {"version": "1.0.3", "bundled": true, "requires": {"asap": "^2.0.0", "wrappy": "1"}, "dependencies": {"asap": {"version": "2.0.5", "bundled": true}}}, "editor": {"version": "1.0.0", "bundled": true}, "find-npm-prefix": {"version": "1.0.2", "bundled": true}, "fs-vacuum": {"version": "1.2.10", "bundled": true, "requires": {"graceful-fs": "^4.1.2", "path-is-inside": "^1.0.1", "rimraf": "^2.5.2"}}, "fs-write-stream-atomic": {"version": "1.0.10", "bundled": true, "requires": {"graceful-fs": "^4.1.2", "iferr": "^0.1.5", "imurmurhash": "^0.1.4", "readable-stream": "1 || 2"}}, "gentle-fs": {"version": "2.0.1", "bundled": true, "requires": {"aproba": "^1.1.2", "fs-vacuum": "^1.2.10", "graceful-fs": "^4.1.11", "iferr": "^0.1.5", "mkdirp": "^0.5.1", "path-is-inside": "^1.0.2", "read-cmd-shim": "^1.0.1", "slide": "^1.1.6"}}, "glob": {"version": "7.1.2", "bundled": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "dependencies": {"fs.realpath": {"version": "1.0.0", "bundled": true}, "minimatch": {"version": "3.0.4", "bundled": true, "requires": {"brace-expansion": "^1.1.7"}, "dependencies": {"brace-expansion": {"version": "1.1.8", "bundled": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}, "dependencies": {"balanced-match": {"version": "1.0.0", "bundled": true}, "concat-map": {"version": "0.0.1", "bundled": true}}}}}, "path-is-absolute": {"version": "1.0.1", "bundled": true}}}, "graceful-fs": {"version": "4.1.11", "bundled": true}, "has-unicode": {"version": "2.0.1", "bundled": true}, "hosted-git-info": {"version": "2.5.0", "bundled": true}, "iferr": {"version": "0.1.5", "bundled": true}, "imurmurhash": {"version": "0.1.4", "bundled": true}, "inflight": {"version": "1.0.6", "bundled": true, "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.3", "bundled": true}, "ini": {"version": "1.3.5", "bundled": true}, "init-package-json": {"version": "1.10.1", "bundled": true, "requires": {"glob": "^7.1.1", "npm-package-arg": "^4.0.0 || ^5.0.0", "promzard": "^0.3.0", "read": "~1.0.1", "read-package-json": "1 || 2", "semver": "2.x || 3.x || 4 || 5", "validate-npm-package-license": "^3.0.1", "validate-npm-package-name": "^3.0.0"}, "dependencies": {"npm-package-arg": {"version": "5.1.2", "bundled": true, "requires": {"hosted-git-info": "^2.4.2", "osenv": "^0.1.4", "semver": "^5.1.0", "validate-npm-package-name": "^3.0.0"}}, "promzard": {"version": "0.3.0", "bundled": true, "requires": {"read": "1"}}}}, "is-cidr": {"version": "1.0.0", "bundled": true, "requires": {"cidr-regex": "1.0.6"}, "dependencies": {"cidr-regex": {"version": "1.0.6", "bundled": true}}}, "lazy-property": {"version": "1.0.0", "bundled": true}, "libcipm": {"version": "1.3.3", "bundled": true, "requires": {"bin-links": "^1.1.0", "bluebird": "^3.5.1", "find-npm-prefix": "^1.0.2", "graceful-fs": "^4.1.11", "lock-verify": "^2.0.0", "npm-lifecycle": "^2.0.0", "npm-logical-tree": "^1.2.1", "npm-package-arg": "^6.0.0", "pacote": "^7.3.3", "protoduck": "^5.0.0", "read-package-json": "^2.0.12", "rimraf": "^2.6.2", "worker-farm": "^1.5.2"}, "dependencies": {"find-npm-prefix": {"version": "1.0.2", "bundled": true}, "lock-verify": {"version": "2.0.0", "bundled": true, "requires": {"npm-package-arg": "^5.1.2", "semver": "^5.4.1"}, "dependencies": {"npm-package-arg": {"version": "5.1.2", "bundled": true, "requires": {"hosted-git-info": "^2.4.2", "osenv": "^0.1.4", "semver": "^5.1.0", "validate-npm-package-name": "^3.0.0"}}}}, "npm-logical-tree": {"version": "1.2.1", "bundled": true}, "protoduck": {"version": "5.0.0", "bundled": true, "requires": {"genfun": "^4.0.1"}, "dependencies": {"genfun": {"version": "4.0.1", "bundled": true}}}, "worker-farm": {"version": "1.5.2", "bundled": true, "requires": {"errno": "^0.1.4", "xtend": "^4.0.1"}, "dependencies": {"errno": {"version": "0.1.7", "bundled": true, "requires": {"prr": "~1.0.1"}, "dependencies": {"prr": {"version": "1.0.1", "bundled": true}}}, "xtend": {"version": "4.0.1", "bundled": true}}}}}, "libnpx": {"version": "9.7.1", "bundled": true, "requires": {"dotenv": "^4.0.0", "npm-package-arg": "^5.1.2", "rimraf": "^2.6.1", "safe-buffer": "^5.1.0", "update-notifier": "^2.2.0", "which": "^1.2.14", "y18n": "^3.2.1", "yargs": "^8.0.2"}, "dependencies": {"dotenv": {"version": "4.0.0", "bundled": true}, "npm-package-arg": {"version": "5.1.2", "bundled": true, "requires": {"hosted-git-info": "^2.4.2", "osenv": "^0.1.4", "semver": "^5.1.0", "validate-npm-package-name": "^3.0.0"}}, "y18n": {"version": "3.2.1", "bundled": true}, "yargs": {"version": "8.0.2", "bundled": true, "requires": {"camelcase": "^4.1.0", "cliui": "^3.2.0", "decamelize": "^1.1.1", "get-caller-file": "^1.0.1", "os-locale": "^2.0.0", "read-pkg-up": "^2.0.0", "require-directory": "^2.1.1", "require-main-filename": "^1.0.1", "set-blocking": "^2.0.0", "string-width": "^2.0.0", "which-module": "^2.0.0", "y18n": "^3.2.1", "yargs-parser": "^7.0.0"}, "dependencies": {"camelcase": {"version": "4.1.0", "bundled": true}, "cliui": {"version": "3.2.0", "bundled": true, "requires": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wrap-ansi": "^2.0.0"}, "dependencies": {"string-width": {"version": "1.0.2", "bundled": true, "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}, "dependencies": {"code-point-at": {"version": "1.1.0", "bundled": true}, "is-fullwidth-code-point": {"version": "1.0.0", "bundled": true, "requires": {"number-is-nan": "^1.0.0"}, "dependencies": {"number-is-nan": {"version": "1.0.1", "bundled": true}}}}}, "strip-ansi": {"version": "3.0.1", "bundled": true, "requires": {"ansi-regex": "^2.0.0"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "bundled": true}}}, "wrap-ansi": {"version": "2.1.0", "bundled": true, "requires": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1"}}}}, "decamelize": {"version": "1.2.0", "bundled": true}, "get-caller-file": {"version": "1.0.2", "bundled": true}, "os-locale": {"version": "2.1.0", "bundled": true, "requires": {"execa": "^0.7.0", "lcid": "^1.0.0", "mem": "^1.1.0"}, "dependencies": {"execa": {"version": "0.7.0", "bundled": true, "requires": {"cross-spawn": "^5.0.1", "get-stream": "^3.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "dependencies": {"cross-spawn": {"version": "5.1.0", "bundled": true, "requires": {"lru-cache": "^4.0.1", "shebang-command": "^1.2.0", "which": "^1.2.9"}, "dependencies": {"shebang-command": {"version": "1.2.0", "bundled": true, "requires": {"shebang-regex": "^1.0.0"}, "dependencies": {"shebang-regex": {"version": "1.0.0", "bundled": true}}}}}, "get-stream": {"version": "3.0.0", "bundled": true}, "is-stream": {"version": "1.1.0", "bundled": true}, "npm-run-path": {"version": "2.0.2", "bundled": true, "requires": {"path-key": "^2.0.0"}, "dependencies": {"path-key": {"version": "2.0.1", "bundled": true}}}, "p-finally": {"version": "1.0.0", "bundled": true}, "signal-exit": {"version": "3.0.2", "bundled": true}, "strip-eof": {"version": "1.0.0", "bundled": true}}}, "lcid": {"version": "1.0.0", "bundled": true, "requires": {"invert-kv": "^1.0.0"}, "dependencies": {"invert-kv": {"version": "1.0.0", "bundled": true}}}, "mem": {"version": "1.1.0", "bundled": true, "requires": {"mimic-fn": "^1.0.0"}, "dependencies": {"mimic-fn": {"version": "1.1.0", "bundled": true}}}}}, "read-pkg-up": {"version": "2.0.0", "bundled": true, "requires": {"find-up": "^2.0.0", "read-pkg": "^2.0.0"}, "dependencies": {"find-up": {"version": "2.1.0", "bundled": true, "requires": {"locate-path": "^2.0.0"}, "dependencies": {"locate-path": {"version": "2.0.0", "bundled": true, "requires": {"p-locate": "^2.0.0", "path-exists": "^3.0.0"}, "dependencies": {"p-locate": {"version": "2.0.0", "bundled": true, "requires": {"p-limit": "^1.1.0"}, "dependencies": {"p-limit": {"version": "1.1.0", "bundled": true}}}, "path-exists": {"version": "3.0.0", "bundled": true}}}}}, "read-pkg": {"version": "2.0.0", "bundled": true, "requires": {"load-json-file": "^2.0.0", "normalize-package-data": "^2.3.2", "path-type": "^2.0.0"}, "dependencies": {"load-json-file": {"version": "2.0.0", "bundled": true, "requires": {"graceful-fs": "^4.1.2", "parse-json": "^2.2.0", "pify": "^2.0.0", "strip-bom": "^3.0.0"}, "dependencies": {"parse-json": {"version": "2.2.0", "bundled": true, "requires": {"error-ex": "^1.2.0"}, "dependencies": {"error-ex": {"version": "1.3.1", "bundled": true, "requires": {"is-arrayish": "^0.2.1"}, "dependencies": {"is-arrayish": {"version": "0.2.1", "bundled": true}}}}}, "pify": {"version": "2.3.0", "bundled": true}, "strip-bom": {"version": "3.0.0", "bundled": true}}}, "path-type": {"version": "2.0.0", "bundled": true, "requires": {"pify": "^2.0.0"}, "dependencies": {"pify": {"version": "2.3.0", "bundled": true}}}}}}}, "require-directory": {"version": "2.1.1", "bundled": true}, "require-main-filename": {"version": "1.0.1", "bundled": true}, "set-blocking": {"version": "2.0.0", "bundled": true}, "string-width": {"version": "2.1.1", "bundled": true, "requires": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}, "dependencies": {"is-fullwidth-code-point": {"version": "2.0.0", "bundled": true}}}, "which-module": {"version": "2.0.0", "bundled": true}, "yargs-parser": {"version": "7.0.0", "bundled": true, "requires": {"camelcase": "^4.1.0"}}}}}}, "lockfile": {"version": "1.0.3", "bundled": true}, "lodash._baseindexof": {"version": "3.1.0", "bundled": true}, "lodash._baseuniq": {"version": "4.6.0", "bundled": true, "requires": {"lodash._createset": "~4.0.0", "lodash._root": "~3.0.0"}, "dependencies": {"lodash._createset": {"version": "4.0.3", "bundled": true}, "lodash._root": {"version": "3.0.1", "bundled": true}}}, "lodash._bindcallback": {"version": "3.0.1", "bundled": true}, "lodash._cacheindexof": {"version": "3.0.2", "bundled": true}, "lodash._createcache": {"version": "3.1.2", "bundled": true, "requires": {"lodash._getnative": "^3.0.0"}}, "lodash._getnative": {"version": "3.9.1", "bundled": true}, "lodash.clonedeep": {"version": "4.5.0", "bundled": true}, "lodash.restparam": {"version": "3.6.1", "bundled": true}, "lodash.union": {"version": "4.6.0", "bundled": true}, "lodash.uniq": {"version": "4.5.0", "bundled": true}, "lodash.without": {"version": "4.4.0", "bundled": true}, "lru-cache": {"version": "4.1.1", "bundled": true, "requires": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}, "dependencies": {"pseudomap": {"version": "1.0.2", "bundled": true}, "yallist": {"version": "2.1.2", "bundled": true}}}, "meant": {"version": "1.0.1", "bundled": true}, "mississippi": {"version": "2.0.0", "bundled": true, "requires": {"concat-stream": "^1.5.0", "duplexify": "^3.4.2", "end-of-stream": "^1.1.0", "flush-write-stream": "^1.0.0", "from2": "^2.1.0", "parallel-transform": "^1.1.0", "pump": "^2.0.1", "pumpify": "^1.3.3", "stream-each": "^1.1.0", "through2": "^2.0.0"}, "dependencies": {"concat-stream": {"version": "1.6.0", "bundled": true, "requires": {"inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}, "dependencies": {"typedarray": {"version": "0.0.6", "bundled": true}}}, "duplexify": {"version": "3.5.3", "bundled": true, "requires": {"end-of-stream": "^1.0.0", "inherits": "^2.0.1", "readable-stream": "^2.0.0", "stream-shift": "^1.0.0"}, "dependencies": {"stream-shift": {"version": "1.0.0", "bundled": true}}}, "end-of-stream": {"version": "1.4.1", "bundled": true, "requires": {"once": "^1.4.0"}}, "flush-write-stream": {"version": "1.0.2", "bundled": true, "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.4"}}, "from2": {"version": "2.3.0", "bundled": true, "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.0"}}, "parallel-transform": {"version": "1.1.0", "bundled": true, "requires": {"cyclist": "~0.2.2", "inherits": "^2.0.3", "readable-stream": "^2.1.5"}, "dependencies": {"cyclist": {"version": "0.2.2", "bundled": true}}}, "pump": {"version": "2.0.1", "bundled": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "pumpify": {"version": "1.4.0", "bundled": true, "requires": {"duplexify": "^3.5.3", "inherits": "^2.0.3", "pump": "^2.0.0"}}, "stream-each": {"version": "1.2.2", "bundled": true, "requires": {"end-of-stream": "^1.1.0", "stream-shift": "^1.0.0"}, "dependencies": {"stream-shift": {"version": "1.0.0", "bundled": true}}}, "through2": {"version": "2.0.3", "bundled": true, "requires": {"readable-stream": "^2.1.5", "xtend": "~4.0.1"}, "dependencies": {"xtend": {"version": "4.0.1", "bundled": true}}}}}, "mkdirp": {"version": "0.5.1", "bundled": true, "requires": {"minimist": "0.0.8"}, "dependencies": {"minimist": {"version": "0.0.8", "bundled": true}}}, "move-concurrently": {"version": "1.0.1", "bundled": true, "requires": {"aproba": "^1.1.1", "copy-concurrently": "^1.0.0", "fs-write-stream-atomic": "^1.0.8", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.3"}, "dependencies": {"copy-concurrently": {"version": "1.0.5", "bundled": true, "requires": {"aproba": "^1.1.1", "fs-write-stream-atomic": "^1.0.8", "iferr": "^0.1.5", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.0"}}, "run-queue": {"version": "1.0.3", "bundled": true, "requires": {"aproba": "^1.1.1"}}}}, "node-gyp": {"version": "3.6.2", "bundled": true, "requires": {"fstream": "^1.0.0", "glob": "^7.0.3", "graceful-fs": "^4.1.2", "minimatch": "^3.0.2", "mkdirp": "^0.5.0", "nopt": "2 || 3", "npmlog": "0 || 1 || 2 || 3 || 4", "osenv": "0", "request": "2", "rimraf": "2", "semver": "~5.3.0", "tar": "^2.0.0", "which": "1"}, "dependencies": {"fstream": {"version": "1.0.11", "bundled": true, "requires": {"graceful-fs": "^4.1.2", "inherits": "~2.0.0", "mkdirp": ">=0.5 0", "rimraf": "2"}}, "minimatch": {"version": "3.0.4", "bundled": true, "requires": {"brace-expansion": "^1.1.7"}, "dependencies": {"brace-expansion": {"version": "1.1.8", "bundled": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}, "dependencies": {"balanced-match": {"version": "1.0.0", "bundled": true}, "concat-map": {"version": "0.0.1", "bundled": true}}}}}, "nopt": {"version": "3.0.6", "bundled": true, "requires": {"abbrev": "1"}}, "semver": {"version": "5.3.0", "bundled": true}, "tar": {"version": "2.2.1", "bundled": true, "requires": {"block-stream": "*", "fstream": "^1.0.2", "inherits": "2"}, "dependencies": {"block-stream": {"version": "0.0.9", "bundled": true, "requires": {"inherits": "~2.0.0"}}}}}}, "nopt": {"version": "4.0.1", "bundled": true, "requires": {"abbrev": "1", "osenv": "^0.1.4"}}, "normalize-package-data": {"version": "2.4.0", "bundled": true, "requires": {"hosted-git-info": "^2.1.4", "is-builtin-module": "^1.0.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}, "dependencies": {"is-builtin-module": {"version": "1.0.0", "bundled": true, "requires": {"builtin-modules": "^1.0.0"}, "dependencies": {"builtin-modules": {"version": "1.1.1", "bundled": true}}}}}, "npm-cache-filename": {"version": "1.0.2", "bundled": true}, "npm-install-checks": {"version": "3.0.0", "bundled": true, "requires": {"semver": "^2.3.0 || 3.x || 4 || 5"}}, "npm-lifecycle": {"version": "2.0.0", "bundled": true, "requires": {"byline": "^5.0.0", "graceful-fs": "^4.1.11", "node-gyp": "^3.6.2", "resolve-from": "^4.0.0", "slide": "^1.1.6", "uid-number": "0.0.6", "umask": "^1.1.0", "which": "^1.3.0"}, "dependencies": {"byline": {"version": "5.0.0", "bundled": true}, "resolve-from": {"version": "4.0.0", "bundled": true}}}, "npm-package-arg": {"version": "6.0.0", "bundled": true, "requires": {"hosted-git-info": "^2.5.0", "osenv": "^0.1.4", "semver": "^5.4.1", "validate-npm-package-name": "^3.0.0"}}, "npm-packlist": {"version": "1.1.10", "bundled": true, "requires": {"ignore-walk": "^3.0.1", "npm-bundled": "^1.0.1"}, "dependencies": {"ignore-walk": {"version": "3.0.1", "bundled": true, "requires": {"minimatch": "^3.0.4"}, "dependencies": {"minimatch": {"version": "3.0.4", "bundled": true, "requires": {"brace-expansion": "^1.1.7"}, "dependencies": {"brace-expansion": {"version": "1.1.8", "bundled": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}, "dependencies": {"balanced-match": {"version": "1.0.0", "bundled": true}, "concat-map": {"version": "0.0.1", "bundled": true}}}}}}}, "npm-bundled": {"version": "1.0.3", "bundled": true}}}, "npm-profile": {"version": "3.0.1", "bundled": true, "requires": {"aproba": "^1.1.2", "make-fetch-happen": "^2.5.0"}, "dependencies": {"make-fetch-happen": {"version": "2.6.0", "bundled": true, "requires": {"agentkeepalive": "^3.3.0", "cacache": "^10.0.0", "http-cache-semantics": "^3.8.0", "http-proxy-agent": "^2.0.0", "https-proxy-agent": "^2.1.0", "lru-cache": "^4.1.1", "mississippi": "^1.2.0", "node-fetch-npm": "^2.0.2", "promise-retry": "^1.1.1", "socks-proxy-agent": "^3.0.1", "ssri": "^5.0.0"}, "dependencies": {"agentkeepalive": {"version": "3.3.0", "bundled": true, "requires": {"humanize-ms": "^1.2.1"}, "dependencies": {"humanize-ms": {"version": "1.2.1", "bundled": true, "requires": {"ms": "^2.0.0"}, "dependencies": {"ms": {"version": "2.1.1", "bundled": true}}}}}, "http-cache-semantics": {"version": "3.8.1", "bundled": true}, "http-proxy-agent": {"version": "2.0.0", "bundled": true, "requires": {"agent-base": "4", "debug": "2"}, "dependencies": {"agent-base": {"version": "4.2.0", "bundled": true, "requires": {"es6-promisify": "^5.0.0"}, "dependencies": {"es6-promisify": {"version": "5.0.0", "bundled": true, "requires": {"es6-promise": "^4.0.3"}, "dependencies": {"es6-promise": {"version": "4.2.4", "bundled": true}}}}}, "debug": {"version": "2.6.9", "bundled": true, "requires": {"ms": "2.0.0"}, "dependencies": {"ms": {"version": "2.0.0", "bundled": true}}}}}, "https-proxy-agent": {"version": "2.1.1", "bundled": true, "requires": {"agent-base": "^4.1.0", "debug": "^3.1.0"}, "dependencies": {"agent-base": {"version": "4.2.0", "bundled": true, "requires": {"es6-promisify": "^5.0.0"}, "dependencies": {"es6-promisify": {"version": "5.0.0", "bundled": true, "requires": {"es6-promise": "^4.0.3"}, "dependencies": {"es6-promise": {"version": "4.2.4", "bundled": true}}}}}, "debug": {"version": "3.1.0", "bundled": true, "requires": {"ms": "2.0.0"}, "dependencies": {"ms": {"version": "2.0.0", "bundled": true}}}}}, "mississippi": {"version": "1.3.1", "bundled": true, "requires": {"concat-stream": "^1.5.0", "duplexify": "^3.4.2", "end-of-stream": "^1.1.0", "flush-write-stream": "^1.0.0", "from2": "^2.1.0", "parallel-transform": "^1.1.0", "pump": "^1.0.0", "pumpify": "^1.3.3", "stream-each": "^1.1.0", "through2": "^2.0.0"}, "dependencies": {"concat-stream": {"version": "1.6.0", "bundled": true, "requires": {"inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}, "dependencies": {"typedarray": {"version": "0.0.6", "bundled": true}}}, "duplexify": {"version": "3.5.3", "bundled": true, "requires": {"end-of-stream": "^1.0.0", "inherits": "^2.0.1", "readable-stream": "^2.0.0", "stream-shift": "^1.0.0"}, "dependencies": {"stream-shift": {"version": "1.0.0", "bundled": true}}}, "end-of-stream": {"version": "1.4.1", "bundled": true, "requires": {"once": "^1.4.0"}}, "flush-write-stream": {"version": "1.0.2", "bundled": true, "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.4"}}, "from2": {"version": "2.3.0", "bundled": true, "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.0"}}, "parallel-transform": {"version": "1.1.0", "bundled": true, "requires": {"cyclist": "~0.2.2", "inherits": "^2.0.3", "readable-stream": "^2.1.5"}, "dependencies": {"cyclist": {"version": "0.2.2", "bundled": true}}}, "pump": {"version": "1.0.3", "bundled": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "pumpify": {"version": "1.4.0", "bundled": true, "requires": {"duplexify": "^3.5.3", "inherits": "^2.0.3", "pump": "^2.0.0"}, "dependencies": {"pump": {"version": "2.0.1", "bundled": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}}}, "stream-each": {"version": "1.2.2", "bundled": true, "requires": {"end-of-stream": "^1.1.0", "stream-shift": "^1.0.0"}, "dependencies": {"stream-shift": {"version": "1.0.0", "bundled": true}}}, "through2": {"version": "2.0.3", "bundled": true, "requires": {"readable-stream": "^2.1.5", "xtend": "~4.0.1"}, "dependencies": {"xtend": {"version": "4.0.1", "bundled": true}}}}}, "node-fetch-npm": {"version": "2.0.2", "bundled": true, "requires": {"encoding": "^0.1.11", "json-parse-better-errors": "^1.0.0", "safe-buffer": "^5.1.1"}, "dependencies": {"encoding": {"version": "0.1.12", "bundled": true, "requires": {"iconv-lite": "~0.4.13"}, "dependencies": {"iconv-lite": {"version": "0.4.19", "bundled": true}}}, "json-parse-better-errors": {"version": "1.0.1", "bundled": true}}}, "promise-retry": {"version": "1.1.1", "bundled": true, "requires": {"err-code": "^1.0.0", "retry": "^0.10.0"}, "dependencies": {"err-code": {"version": "1.1.2", "bundled": true}}}, "socks-proxy-agent": {"version": "3.0.1", "bundled": true, "requires": {"agent-base": "^4.1.0", "socks": "^1.1.10"}, "dependencies": {"agent-base": {"version": "4.2.0", "bundled": true, "requires": {"es6-promisify": "^5.0.0"}, "dependencies": {"es6-promisify": {"version": "5.0.0", "bundled": true, "requires": {"es6-promise": "^4.0.3"}, "dependencies": {"es6-promise": {"version": "4.2.4", "bundled": true}}}}}, "socks": {"version": "1.1.10", "bundled": true, "requires": {"ip": "^1.1.4", "smart-buffer": "^1.0.13"}, "dependencies": {"ip": {"version": "1.1.5", "bundled": true}, "smart-buffer": {"version": "1.1.15", "bundled": true}}}}}}}}}, "npm-registry-client": {"version": "8.5.0", "bundled": true, "requires": {"concat-stream": "^1.5.2", "graceful-fs": "^4.1.6", "normalize-package-data": "~1.0.1 || ^2.0.0", "npm-package-arg": "^3.0.0 || ^4.0.0 || ^5.0.0", "npmlog": "2 || ^3.1.0 || ^4.0.0", "once": "^1.3.3", "request": "^2.74.0", "retry": "^0.10.0", "semver": "2 >=2.2.1 || 3.x || 4 || 5", "slide": "^1.1.3", "ssri": "^4.1.2"}, "dependencies": {"concat-stream": {"version": "1.6.0", "bundled": true, "requires": {"inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}, "dependencies": {"typedarray": {"version": "0.0.6", "bundled": true}}}, "npm-package-arg": {"version": "5.1.2", "bundled": true, "requires": {"hosted-git-info": "^2.4.2", "osenv": "^0.1.4", "semver": "^5.1.0", "validate-npm-package-name": "^3.0.0"}}, "ssri": {"version": "4.1.6", "bundled": true, "requires": {"safe-buffer": "^5.1.0"}}}}, "npm-user-validate": {"version": "1.0.0", "bundled": true}, "npmlog": {"version": "4.1.2", "bundled": true, "requires": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.7.3", "set-blocking": "~2.0.0"}, "dependencies": {"are-we-there-yet": {"version": "1.1.4", "bundled": true, "requires": {"delegates": "^1.0.0", "readable-stream": "^2.0.6"}, "dependencies": {"delegates": {"version": "1.0.0", "bundled": true}}}, "console-control-strings": {"version": "1.1.0", "bundled": true}, "gauge": {"version": "2.7.4", "bundled": true, "requires": {"aproba": "^1.0.3", "console-control-strings": "^1.0.0", "has-unicode": "^2.0.0", "object-assign": "^4.1.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0"}, "dependencies": {"object-assign": {"version": "4.1.1", "bundled": true}, "signal-exit": {"version": "3.0.2", "bundled": true}, "string-width": {"version": "1.0.2", "bundled": true, "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}, "dependencies": {"code-point-at": {"version": "1.1.0", "bundled": true}, "is-fullwidth-code-point": {"version": "1.0.0", "bundled": true, "requires": {"number-is-nan": "^1.0.0"}, "dependencies": {"number-is-nan": {"version": "1.0.1", "bundled": true}}}}}, "strip-ansi": {"version": "3.0.1", "bundled": true, "requires": {"ansi-regex": "^2.0.0"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "bundled": true}}}, "wide-align": {"version": "1.1.2", "bundled": true, "requires": {"string-width": "^1.0.2"}}}}, "set-blocking": {"version": "2.0.0", "bundled": true}}}, "once": {"version": "1.4.0", "bundled": true, "requires": {"wrappy": "1"}}, "opener": {"version": "1.4.3", "bundled": true}, "osenv": {"version": "0.1.5", "bundled": true, "requires": {"os-homedir": "^1.0.0", "os-tmpdir": "^1.0.0"}, "dependencies": {"os-homedir": {"version": "1.0.2", "bundled": true}, "os-tmpdir": {"version": "1.0.2", "bundled": true}}}, "pacote": {"version": "7.3.3", "bundled": true, "requires": {"bluebird": "^3.5.1", "cacache": "^10.0.2", "get-stream": "^3.0.0", "glob": "^7.1.2", "lru-cache": "^4.1.1", "make-fetch-happen": "^2.6.0", "minimatch": "^3.0.4", "mississippi": "^2.0.0", "normalize-package-data": "^2.4.0", "npm-package-arg": "^6.0.0", "npm-packlist": "^1.1.10", "npm-pick-manifest": "^2.1.0", "osenv": "^0.1.4", "promise-inflight": "^1.0.1", "promise-retry": "^1.1.1", "protoduck": "^5.0.0", "safe-buffer": "^5.1.1", "semver": "^5.5.0", "ssri": "^5.2.1", "tar": "^4.3.3", "unique-filename": "^1.1.0", "which": "^1.3.0"}, "dependencies": {"get-stream": {"version": "3.0.0", "bundled": true}, "make-fetch-happen": {"version": "2.6.0", "bundled": true, "requires": {"agentkeepalive": "^3.3.0", "cacache": "^10.0.0", "http-cache-semantics": "^3.8.0", "http-proxy-agent": "^2.0.0", "https-proxy-agent": "^2.1.0", "lru-cache": "^4.1.1", "mississippi": "^1.2.0", "node-fetch-npm": "^2.0.2", "promise-retry": "^1.1.1", "socks-proxy-agent": "^3.0.1", "ssri": "^5.0.0"}, "dependencies": {"agentkeepalive": {"version": "3.3.0", "bundled": true, "requires": {"humanize-ms": "^1.2.1"}, "dependencies": {"humanize-ms": {"version": "1.2.1", "bundled": true, "requires": {"ms": "^2.0.0"}, "dependencies": {"ms": {"version": "2.1.1", "bundled": true}}}}}, "http-cache-semantics": {"version": "3.8.1", "bundled": true}, "http-proxy-agent": {"version": "2.0.0", "bundled": true, "requires": {"agent-base": "4", "debug": "2"}, "dependencies": {"agent-base": {"version": "4.2.0", "bundled": true, "requires": {"es6-promisify": "^5.0.0"}, "dependencies": {"es6-promisify": {"version": "5.0.0", "bundled": true, "requires": {"es6-promise": "^4.0.3"}, "dependencies": {"es6-promise": {"version": "4.2.4", "bundled": true}}}}}, "debug": {"version": "2.6.9", "bundled": true, "requires": {"ms": "2.0.0"}, "dependencies": {"ms": {"version": "2.0.0", "bundled": true}}}}}, "https-proxy-agent": {"version": "2.1.1", "bundled": true, "requires": {"agent-base": "^4.1.0", "debug": "^3.1.0"}, "dependencies": {"agent-base": {"version": "4.2.0", "bundled": true, "requires": {"es6-promisify": "^5.0.0"}, "dependencies": {"es6-promisify": {"version": "5.0.0", "bundled": true, "requires": {"es6-promise": "^4.0.3"}, "dependencies": {"es6-promise": {"version": "4.2.4", "bundled": true}}}}}, "debug": {"version": "3.1.0", "bundled": true, "requires": {"ms": "2.0.0"}, "dependencies": {"ms": {"version": "2.0.0", "bundled": true}}}}}, "mississippi": {"version": "1.3.1", "bundled": true, "requires": {"concat-stream": "^1.5.0", "duplexify": "^3.4.2", "end-of-stream": "^1.1.0", "flush-write-stream": "^1.0.0", "from2": "^2.1.0", "parallel-transform": "^1.1.0", "pump": "^1.0.0", "pumpify": "^1.3.3", "stream-each": "^1.1.0", "through2": "^2.0.0"}, "dependencies": {"concat-stream": {"version": "1.6.0", "bundled": true, "requires": {"inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}, "dependencies": {"typedarray": {"version": "0.0.6", "bundled": true}}}, "duplexify": {"version": "3.5.3", "bundled": true, "requires": {"end-of-stream": "^1.0.0", "inherits": "^2.0.1", "readable-stream": "^2.0.0", "stream-shift": "^1.0.0"}, "dependencies": {"stream-shift": {"version": "1.0.0", "bundled": true}}}, "end-of-stream": {"version": "1.4.1", "bundled": true, "requires": {"once": "^1.4.0"}}, "flush-write-stream": {"version": "1.0.2", "bundled": true, "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.4"}}, "from2": {"version": "2.3.0", "bundled": true, "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.0"}}, "parallel-transform": {"version": "1.1.0", "bundled": true, "requires": {"cyclist": "~0.2.2", "inherits": "^2.0.3", "readable-stream": "^2.1.5"}, "dependencies": {"cyclist": {"version": "0.2.2", "bundled": true}}}, "pump": {"version": "1.0.3", "bundled": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "pumpify": {"version": "1.4.0", "bundled": true, "requires": {"duplexify": "^3.5.3", "inherits": "^2.0.3", "pump": "^2.0.0"}, "dependencies": {"pump": {"version": "2.0.1", "bundled": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}}}, "stream-each": {"version": "1.2.2", "bundled": true, "requires": {"end-of-stream": "^1.1.0", "stream-shift": "^1.0.0"}, "dependencies": {"stream-shift": {"version": "1.0.0", "bundled": true}}}, "through2": {"version": "2.0.3", "bundled": true, "requires": {"readable-stream": "^2.1.5", "xtend": "~4.0.1"}, "dependencies": {"xtend": {"version": "4.0.1", "bundled": true}}}}}, "node-fetch-npm": {"version": "2.0.2", "bundled": true, "requires": {"encoding": "^0.1.11", "json-parse-better-errors": "^1.0.0", "safe-buffer": "^5.1.1"}, "dependencies": {"encoding": {"version": "0.1.12", "bundled": true, "requires": {"iconv-lite": "~0.4.13"}, "dependencies": {"iconv-lite": {"version": "0.4.19", "bundled": true}}}, "json-parse-better-errors": {"version": "1.0.1", "bundled": true}}}, "socks-proxy-agent": {"version": "3.0.1", "bundled": true, "requires": {"agent-base": "^4.1.0", "socks": "^1.1.10"}, "dependencies": {"agent-base": {"version": "4.2.0", "bundled": true, "requires": {"es6-promisify": "^5.0.0"}, "dependencies": {"es6-promisify": {"version": "5.0.0", "bundled": true, "requires": {"es6-promise": "^4.0.3"}, "dependencies": {"es6-promise": {"version": "4.2.4", "bundled": true}}}}}, "socks": {"version": "1.1.10", "bundled": true, "requires": {"ip": "^1.1.4", "smart-buffer": "^1.0.13"}, "dependencies": {"ip": {"version": "1.1.5", "bundled": true}, "smart-buffer": {"version": "1.1.15", "bundled": true}}}}}}}, "minimatch": {"version": "3.0.4", "bundled": true, "requires": {"brace-expansion": "^1.1.7"}, "dependencies": {"brace-expansion": {"version": "1.1.11", "bundled": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}, "dependencies": {"balanced-match": {"version": "1.0.0", "bundled": true}, "concat-map": {"version": "0.0.1", "bundled": true}}}}}, "mississippi": {"version": "2.0.0", "bundled": true, "requires": {"concat-stream": "^1.5.0", "duplexify": "^3.4.2", "end-of-stream": "^1.1.0", "flush-write-stream": "^1.0.0", "from2": "^2.1.0", "parallel-transform": "^1.1.0", "pump": "^2.0.1", "pumpify": "^1.3.3", "stream-each": "^1.1.0", "through2": "^2.0.0"}, "dependencies": {"concat-stream": {"version": "1.6.0", "bundled": true, "requires": {"inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}, "dependencies": {"typedarray": {"version": "0.0.6", "bundled": true}}}, "duplexify": {"version": "3.5.3", "bundled": true, "requires": {"end-of-stream": "^1.0.0", "inherits": "^2.0.1", "readable-stream": "^2.0.0", "stream-shift": "^1.0.0"}, "dependencies": {"stream-shift": {"version": "1.0.0", "bundled": true}}}, "end-of-stream": {"version": "1.4.1", "bundled": true, "requires": {"once": "^1.4.0"}}, "flush-write-stream": {"version": "1.0.2", "bundled": true, "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.4"}}, "from2": {"version": "2.3.0", "bundled": true, "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.0"}}, "parallel-transform": {"version": "1.1.0", "bundled": true, "requires": {"cyclist": "~0.2.2", "inherits": "^2.0.3", "readable-stream": "^2.1.5"}, "dependencies": {"cyclist": {"version": "0.2.2", "bundled": true}}}, "pump": {"version": "2.0.1", "bundled": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "pumpify": {"version": "1.4.0", "bundled": true, "requires": {"duplexify": "^3.5.3", "inherits": "^2.0.3", "pump": "^2.0.0"}}, "stream-each": {"version": "1.2.2", "bundled": true, "requires": {"end-of-stream": "^1.1.0", "stream-shift": "^1.0.0"}, "dependencies": {"stream-shift": {"version": "1.0.0", "bundled": true}}}, "through2": {"version": "2.0.3", "bundled": true, "requires": {"readable-stream": "^2.1.5", "xtend": "~4.0.1"}, "dependencies": {"xtend": {"version": "4.0.1", "bundled": true}}}}}, "npm-pick-manifest": {"version": "2.1.0", "bundled": true, "requires": {"npm-package-arg": "^6.0.0", "semver": "^5.4.1"}}, "promise-retry": {"version": "1.1.1", "bundled": true, "requires": {"err-code": "^1.0.0", "retry": "^0.10.0"}, "dependencies": {"err-code": {"version": "1.1.2", "bundled": true}}}, "protoduck": {"version": "5.0.0", "bundled": true, "requires": {"genfun": "^4.0.1"}, "dependencies": {"genfun": {"version": "4.0.1", "bundled": true}}}, "semver": {"version": "5.5.0", "bundled": true}}}, "path-is-inside": {"version": "1.0.2", "bundled": true}, "promise-inflight": {"version": "1.0.1", "bundled": true}, "qrcode-terminal": {"version": "0.11.0", "bundled": true}, "query-string": {"version": "5.1.0", "bundled": true, "requires": {"decode-uri-component": "^0.2.0", "object-assign": "^4.1.0", "strict-uri-encode": "^1.0.0"}, "dependencies": {"decode-uri-component": {"version": "0.2.0", "bundled": true}, "object-assign": {"version": "4.1.1", "bundled": true}, "strict-uri-encode": {"version": "1.1.0", "bundled": true}}}, "qw": {"version": "1.0.1", "bundled": true}, "read": {"version": "1.0.7", "bundled": true, "requires": {"mute-stream": "~0.0.4"}, "dependencies": {"mute-stream": {"version": "0.0.7", "bundled": true}}}, "read-cmd-shim": {"version": "1.0.1", "bundled": true, "requires": {"graceful-fs": "^4.1.2"}}, "read-installed": {"version": "4.0.3", "bundled": true, "requires": {"debuglog": "^1.0.1", "graceful-fs": "^4.1.2", "read-package-json": "^2.0.0", "readdir-scoped-modules": "^1.0.0", "semver": "2 || 3 || 4 || 5", "slide": "~1.1.3", "util-extend": "^1.0.1"}, "dependencies": {"util-extend": {"version": "1.0.3", "bundled": true}}}, "read-package-json": {"version": "2.0.12", "bundled": true, "requires": {"glob": "^7.1.1", "graceful-fs": "^4.1.2", "json-parse-better-errors": "^1.0.0", "normalize-package-data": "^2.0.0", "slash": "^1.0.0"}, "dependencies": {"json-parse-better-errors": {"version": "1.0.1", "bundled": true}, "slash": {"version": "1.0.0", "bundled": true}}}, "read-package-tree": {"version": "5.1.6", "bundled": true, "requires": {"debuglog": "^1.0.1", "dezalgo": "^1.0.0", "once": "^1.3.0", "read-package-json": "^2.0.0", "readdir-scoped-modules": "^1.0.0"}}, "readable-stream": {"version": "2.3.4", "bundled": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.0.3", "util-deprecate": "~1.0.1"}, "dependencies": {"core-util-is": {"version": "1.0.2", "bundled": true}, "isarray": {"version": "1.0.0", "bundled": true}, "process-nextick-args": {"version": "2.0.0", "bundled": true}, "string_decoder": {"version": "1.0.3", "bundled": true, "requires": {"safe-buffer": "~5.1.0"}}, "util-deprecate": {"version": "1.0.2", "bundled": true}}}, "readdir-scoped-modules": {"version": "1.0.2", "bundled": true, "requires": {"debuglog": "^1.0.1", "dezalgo": "^1.0.0", "graceful-fs": "^4.1.2", "once": "^1.3.0"}}, "request": {"version": "2.83.0", "bundled": true, "requires": {"aws-sign2": "~0.7.0", "aws4": "^1.6.0", "caseless": "~0.12.0", "combined-stream": "~1.0.5", "extend": "~3.0.1", "forever-agent": "~0.6.1", "form-data": "~2.3.1", "har-validator": "~5.0.3", "hawk": "~6.0.2", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.17", "oauth-sign": "~0.8.2", "performance-now": "^2.1.0", "qs": "~6.5.1", "safe-buffer": "^5.1.1", "stringstream": "~0.0.5", "tough-cookie": "~2.3.3", "tunnel-agent": "^0.6.0", "uuid": "^3.1.0"}, "dependencies": {"aws-sign2": {"version": "0.7.0", "bundled": true}, "aws4": {"version": "1.6.0", "bundled": true}, "caseless": {"version": "0.12.0", "bundled": true}, "combined-stream": {"version": "1.0.5", "bundled": true, "requires": {"delayed-stream": "~1.0.0"}, "dependencies": {"delayed-stream": {"version": "1.0.0", "bundled": true}}}, "extend": {"version": "3.0.1", "bundled": true}, "forever-agent": {"version": "0.6.1", "bundled": true}, "form-data": {"version": "2.3.1", "bundled": true, "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.5", "mime-types": "^2.1.12"}, "dependencies": {"asynckit": {"version": "0.4.0", "bundled": true}}}, "har-validator": {"version": "5.0.3", "bundled": true, "requires": {"ajv": "^5.1.0", "har-schema": "^2.0.0"}, "dependencies": {"ajv": {"version": "5.2.3", "bundled": true, "requires": {"co": "^4.6.0", "fast-deep-equal": "^1.0.0", "json-schema-traverse": "^0.3.0", "json-stable-stringify": "^1.0.1"}, "dependencies": {"co": {"version": "4.6.0", "bundled": true}, "fast-deep-equal": {"version": "1.0.0", "bundled": true}, "json-schema-traverse": {"version": "0.3.1", "bundled": true}, "json-stable-stringify": {"version": "1.0.1", "bundled": true, "requires": {"jsonify": "~0.0.0"}, "dependencies": {"jsonify": {"version": "0.0.0", "bundled": true}}}}}, "har-schema": {"version": "2.0.0", "bundled": true}}}, "hawk": {"version": "6.0.2", "bundled": true, "requires": {"boom": "4.x.x", "cryptiles": "3.x.x", "hoek": "4.x.x", "sntp": "2.x.x"}, "dependencies": {"boom": {"version": "4.3.1", "bundled": true, "requires": {"hoek": "4.x.x"}}, "cryptiles": {"version": "3.1.2", "bundled": true, "requires": {"boom": "5.x.x"}, "dependencies": {"boom": {"version": "5.2.0", "bundled": true, "requires": {"hoek": "4.x.x"}}}}, "hoek": {"version": "4.2.0", "bundled": true}, "sntp": {"version": "2.0.2", "bundled": true, "requires": {"hoek": "4.x.x"}}}}, "http-signature": {"version": "1.2.0", "bundled": true, "requires": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}, "dependencies": {"assert-plus": {"version": "1.0.0", "bundled": true}, "jsprim": {"version": "1.4.1", "bundled": true, "requires": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.2.3", "verror": "1.10.0"}, "dependencies": {"extsprintf": {"version": "1.3.0", "bundled": true}, "json-schema": {"version": "0.2.3", "bundled": true}, "verror": {"version": "1.10.0", "bundled": true, "requires": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}, "dependencies": {"core-util-is": {"version": "1.0.2", "bundled": true}}}}}, "sshpk": {"version": "1.13.1", "bundled": true, "requires": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "tweetnacl": "~0.14.0"}, "dependencies": {"asn1": {"version": "0.2.3", "bundled": true}, "bcrypt-pbkdf": {"version": "1.0.1", "bundled": true, "optional": true, "requires": {"tweetnacl": "^0.14.3"}}, "dashdash": {"version": "1.14.1", "bundled": true, "requires": {"assert-plus": "^1.0.0"}}, "ecc-jsbn": {"version": "0.1.1", "bundled": true, "optional": true, "requires": {"jsbn": "~0.1.0"}}, "getpass": {"version": "0.1.7", "bundled": true, "requires": {"assert-plus": "^1.0.0"}}, "jsbn": {"version": "0.1.1", "bundled": true, "optional": true}, "tweetnacl": {"version": "0.14.5", "bundled": true, "optional": true}}}}}, "is-typedarray": {"version": "1.0.0", "bundled": true}, "isstream": {"version": "0.1.2", "bundled": true}, "json-stringify-safe": {"version": "5.0.1", "bundled": true}, "mime-types": {"version": "2.1.17", "bundled": true, "requires": {"mime-db": "~1.30.0"}, "dependencies": {"mime-db": {"version": "1.30.0", "bundled": true}}}, "oauth-sign": {"version": "0.8.2", "bundled": true}, "performance-now": {"version": "2.1.0", "bundled": true}, "qs": {"version": "6.5.1", "bundled": true}, "stringstream": {"version": "0.0.5", "bundled": true}, "tough-cookie": {"version": "2.3.3", "bundled": true, "requires": {"punycode": "^1.4.1"}, "dependencies": {"punycode": {"version": "1.4.1", "bundled": true}}}, "tunnel-agent": {"version": "0.6.0", "bundled": true, "requires": {"safe-buffer": "^5.0.1"}}}}, "retry": {"version": "0.10.1", "bundled": true}, "rimraf": {"version": "2.6.2", "bundled": true, "requires": {"glob": "^7.0.5"}}, "safe-buffer": {"version": "5.1.1", "bundled": true}, "semver": {"version": "5.5.0", "bundled": true}, "sha": {"version": "2.0.1", "bundled": true, "requires": {"graceful-fs": "^4.1.2", "readable-stream": "^2.0.2"}}, "slide": {"version": "1.1.6", "bundled": true}, "sorted-object": {"version": "2.0.1", "bundled": true}, "sorted-union-stream": {"version": "2.1.3", "bundled": true, "requires": {"from2": "^1.3.0", "stream-iterate": "^1.1.0"}, "dependencies": {"from2": {"version": "1.3.0", "bundled": true, "requires": {"inherits": "~2.0.1", "readable-stream": "~1.1.10"}, "dependencies": {"readable-stream": {"version": "1.1.14", "bundled": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}, "dependencies": {"core-util-is": {"version": "1.0.2", "bundled": true}, "isarray": {"version": "0.0.1", "bundled": true}, "string_decoder": {"version": "0.10.31", "bundled": true}}}}}, "stream-iterate": {"version": "1.2.0", "bundled": true, "requires": {"readable-stream": "^2.1.5", "stream-shift": "^1.0.0"}, "dependencies": {"stream-shift": {"version": "1.0.0", "bundled": true}}}}}, "ssri": {"version": "5.2.4", "bundled": true, "requires": {"safe-buffer": "^5.1.1"}}, "strip-ansi": {"version": "4.0.0", "bundled": true, "requires": {"ansi-regex": "^3.0.0"}, "dependencies": {"ansi-regex": {"version": "3.0.0", "bundled": true}}}, "tar": {"version": "4.3.3", "bundled": true, "requires": {"chownr": "^1.0.1", "fs-minipass": "^1.2.3", "minipass": "^2.2.1", "minizlib": "^1.1.0", "mkdirp": "^0.5.0", "yallist": "^3.0.2"}, "dependencies": {"fs-minipass": {"version": "1.2.5", "bundled": true, "requires": {"minipass": "^2.2.1"}}, "minipass": {"version": "2.2.1", "bundled": true, "requires": {"yallist": "^3.0.0"}}, "minizlib": {"version": "1.1.0", "bundled": true, "requires": {"minipass": "^2.2.1"}}, "yallist": {"version": "3.0.2", "bundled": true}}}, "text-table": {"version": "0.2.0", "bundled": true}, "uid-number": {"version": "0.0.6", "bundled": true}, "umask": {"version": "1.1.0", "bundled": true}, "unique-filename": {"version": "1.1.0", "bundled": true, "requires": {"unique-slug": "^2.0.0"}, "dependencies": {"unique-slug": {"version": "2.0.0", "bundled": true, "requires": {"imurmurhash": "^0.1.4"}}}}, "unpipe": {"version": "1.0.0", "bundled": true}, "update-notifier": {"version": "2.3.0", "bundled": true, "requires": {"boxen": "^1.2.1", "chalk": "^2.0.1", "configstore": "^3.0.0", "import-lazy": "^2.1.0", "is-installed-globally": "^0.1.0", "is-npm": "^1.0.0", "latest-version": "^3.0.0", "semver-diff": "^2.0.0", "xdg-basedir": "^3.0.0"}, "dependencies": {"boxen": {"version": "1.2.1", "bundled": true, "requires": {"ansi-align": "^2.0.0", "camelcase": "^4.0.0", "chalk": "^2.0.1", "cli-boxes": "^1.0.0", "string-width": "^2.0.0", "term-size": "^1.2.0", "widest-line": "^1.0.0"}, "dependencies": {"ansi-align": {"version": "2.0.0", "bundled": true, "requires": {"string-width": "^2.0.0"}}, "camelcase": {"version": "4.1.0", "bundled": true}, "cli-boxes": {"version": "1.0.0", "bundled": true}, "string-width": {"version": "2.1.1", "bundled": true, "requires": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}, "dependencies": {"is-fullwidth-code-point": {"version": "2.0.0", "bundled": true}}}, "term-size": {"version": "1.2.0", "bundled": true, "requires": {"execa": "^0.7.0"}, "dependencies": {"execa": {"version": "0.7.0", "bundled": true, "requires": {"cross-spawn": "^5.0.1", "get-stream": "^3.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "dependencies": {"cross-spawn": {"version": "5.1.0", "bundled": true, "requires": {"lru-cache": "^4.0.1", "shebang-command": "^1.2.0", "which": "^1.2.9"}, "dependencies": {"shebang-command": {"version": "1.2.0", "bundled": true, "requires": {"shebang-regex": "^1.0.0"}, "dependencies": {"shebang-regex": {"version": "1.0.0", "bundled": true}}}}}, "get-stream": {"version": "3.0.0", "bundled": true}, "is-stream": {"version": "1.1.0", "bundled": true}, "npm-run-path": {"version": "2.0.2", "bundled": true, "requires": {"path-key": "^2.0.0"}, "dependencies": {"path-key": {"version": "2.0.1", "bundled": true}}}, "p-finally": {"version": "1.0.0", "bundled": true}, "signal-exit": {"version": "3.0.2", "bundled": true}, "strip-eof": {"version": "1.0.0", "bundled": true}}}}}, "widest-line": {"version": "1.0.0", "bundled": true, "requires": {"string-width": "^1.0.1"}, "dependencies": {"string-width": {"version": "1.0.2", "bundled": true, "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}, "dependencies": {"code-point-at": {"version": "1.1.0", "bundled": true}, "is-fullwidth-code-point": {"version": "1.0.0", "bundled": true, "requires": {"number-is-nan": "^1.0.0"}, "dependencies": {"number-is-nan": {"version": "1.0.1", "bundled": true}}}, "strip-ansi": {"version": "3.0.1", "bundled": true, "requires": {"ansi-regex": "^2.0.0"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "bundled": true}}}}}}}}}, "chalk": {"version": "2.1.0", "bundled": true, "requires": {"ansi-styles": "^3.1.0", "escape-string-regexp": "^1.0.5", "supports-color": "^4.0.0"}, "dependencies": {"ansi-styles": {"version": "3.2.0", "bundled": true, "requires": {"color-convert": "^1.9.0"}, "dependencies": {"color-convert": {"version": "1.9.0", "bundled": true, "requires": {"color-name": "^1.1.1"}, "dependencies": {"color-name": {"version": "1.1.3", "bundled": true}}}}}, "escape-string-regexp": {"version": "1.0.5", "bundled": true}, "supports-color": {"version": "4.4.0", "bundled": true, "requires": {"has-flag": "^2.0.0"}, "dependencies": {"has-flag": {"version": "2.0.0", "bundled": true}}}}}, "configstore": {"version": "3.1.1", "bundled": true, "requires": {"dot-prop": "^4.1.0", "graceful-fs": "^4.1.2", "make-dir": "^1.0.0", "unique-string": "^1.0.0", "write-file-atomic": "^2.0.0", "xdg-basedir": "^3.0.0"}, "dependencies": {"dot-prop": {"version": "4.2.0", "bundled": true, "requires": {"is-obj": "^1.0.0"}, "dependencies": {"is-obj": {"version": "1.0.1", "bundled": true}}}, "make-dir": {"version": "1.0.0", "bundled": true, "requires": {"pify": "^2.3.0"}, "dependencies": {"pify": {"version": "2.3.0", "bundled": true}}}, "unique-string": {"version": "1.0.0", "bundled": true, "requires": {"crypto-random-string": "^1.0.0"}, "dependencies": {"crypto-random-string": {"version": "1.0.0", "bundled": true}}}}}, "import-lazy": {"version": "2.1.0", "bundled": true}, "is-installed-globally": {"version": "0.1.0", "bundled": true, "requires": {"global-dirs": "^0.1.0", "is-path-inside": "^1.0.0"}, "dependencies": {"global-dirs": {"version": "0.1.0", "bundled": true, "requires": {"ini": "^1.3.4"}}, "is-path-inside": {"version": "1.0.0", "bundled": true, "requires": {"path-is-inside": "^1.0.1"}}}}, "is-npm": {"version": "1.0.0", "bundled": true}, "latest-version": {"version": "3.1.0", "bundled": true, "requires": {"package-json": "^4.0.0"}, "dependencies": {"package-json": {"version": "4.0.1", "bundled": true, "requires": {"got": "^6.7.1", "registry-auth-token": "^3.0.1", "registry-url": "^3.0.3", "semver": "^5.1.0"}, "dependencies": {"got": {"version": "6.7.1", "bundled": true, "requires": {"create-error-class": "^3.0.0", "duplexer3": "^0.1.4", "get-stream": "^3.0.0", "is-redirect": "^1.0.0", "is-retry-allowed": "^1.0.0", "is-stream": "^1.0.0", "lowercase-keys": "^1.0.0", "safe-buffer": "^5.0.1", "timed-out": "^4.0.0", "unzip-response": "^2.0.1", "url-parse-lax": "^1.0.0"}, "dependencies": {"create-error-class": {"version": "3.0.2", "bundled": true, "requires": {"capture-stack-trace": "^1.0.0"}, "dependencies": {"capture-stack-trace": {"version": "1.0.0", "bundled": true}}}, "duplexer3": {"version": "0.1.4", "bundled": true}, "get-stream": {"version": "3.0.0", "bundled": true}, "is-redirect": {"version": "1.0.0", "bundled": true}, "is-retry-allowed": {"version": "1.1.0", "bundled": true}, "is-stream": {"version": "1.1.0", "bundled": true}, "lowercase-keys": {"version": "1.0.0", "bundled": true}, "timed-out": {"version": "4.0.1", "bundled": true}, "unzip-response": {"version": "2.0.1", "bundled": true}, "url-parse-lax": {"version": "1.0.0", "bundled": true, "requires": {"prepend-http": "^1.0.1"}, "dependencies": {"prepend-http": {"version": "1.0.4", "bundled": true}}}}}, "registry-auth-token": {"version": "3.3.1", "bundled": true, "requires": {"rc": "^1.1.6", "safe-buffer": "^5.0.1"}, "dependencies": {"rc": {"version": "1.2.1", "bundled": true, "requires": {"deep-extend": "~0.4.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "dependencies": {"deep-extend": {"version": "0.4.2", "bundled": true}, "minimist": {"version": "1.2.0", "bundled": true}, "strip-json-comments": {"version": "2.0.1", "bundled": true}}}}}, "registry-url": {"version": "3.1.0", "bundled": true, "requires": {"rc": "^1.0.1"}, "dependencies": {"rc": {"version": "1.2.1", "bundled": true, "requires": {"deep-extend": "~0.4.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "dependencies": {"deep-extend": {"version": "0.4.2", "bundled": true}, "minimist": {"version": "1.2.0", "bundled": true}, "strip-json-comments": {"version": "2.0.1", "bundled": true}}}}}}}}}, "semver-diff": {"version": "2.1.0", "bundled": true, "requires": {"semver": "^5.0.3"}}, "xdg-basedir": {"version": "3.0.0", "bundled": true}}}, "uuid": {"version": "3.2.1", "bundled": true}, "validate-npm-package-license": {"version": "3.0.1", "bundled": true, "requires": {"spdx-correct": "~1.0.0", "spdx-expression-parse": "~1.0.0"}, "dependencies": {"spdx-correct": {"version": "1.0.2", "bundled": true, "requires": {"spdx-license-ids": "^1.0.2"}, "dependencies": {"spdx-license-ids": {"version": "1.2.2", "bundled": true}}}, "spdx-expression-parse": {"version": "1.0.4", "bundled": true}}}, "validate-npm-package-name": {"version": "3.0.0", "bundled": true, "requires": {"builtins": "^1.0.3"}, "dependencies": {"builtins": {"version": "1.0.3", "bundled": true}}}, "which": {"version": "1.3.0", "bundled": true, "requires": {"isexe": "^2.0.0"}, "dependencies": {"isexe": {"version": "2.0.0", "bundled": true}}}, "worker-farm": {"version": "1.5.2", "bundled": true, "requires": {"errno": "^0.1.4", "xtend": "^4.0.1"}, "dependencies": {"errno": {"version": "0.1.7", "bundled": true, "requires": {"prr": "~1.0.1"}, "dependencies": {"prr": {"version": "1.0.1", "bundled": true}}}, "xtend": {"version": "4.0.1", "bundled": true}}}, "wrappy": {"version": "1.0.2", "bundled": true}, "write-file-atomic": {"version": "2.1.0", "bundled": true, "requires": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "slide": "^1.1.5"}}}}, "nssocket": {"version": "0.6.0", "resolved": "https://registry.npmjs.org/nssocket/-/nssocket-0.6.0.tgz", "integrity": "sha1-Wflvb/MhVm8zxw99vu7N/cBxVPo=", "requires": {"eventemitter2": "~0.4.14", "lazy": "~1.0.11"}, "dependencies": {"eventemitter2": {"version": "0.4.14", "resolved": "https://registry.npmjs.org/eventemitter2/-/eventemitter2-0.4.14.tgz", "integrity": "sha1-j2G3XN4BKy6esoTUVFWDtWQ7Yas="}}}, "nyc": {"version": "11.5.0", "resolved": "https://registry.npmjs.org/nyc/-/nyc-11.5.0.tgz", "integrity": "sha512-xIYK189By0YiM5/T4TviHu3J7bV7lCj5WYJfyZK3z03QgAaQ60WcLaJuXf0zhKoI6PBnUR92ZpSwBICCrgSBGg==", "dev": true, "requires": {"archy": "^1.0.0", "arrify": "^1.0.1", "caching-transform": "^1.0.0", "convert-source-map": "^1.5.1", "debug-log": "^1.0.1", "default-require-extensions": "^1.0.0", "find-cache-dir": "^0.1.1", "find-up": "^2.1.0", "foreground-child": "^1.5.3", "glob": "^7.0.6", "istanbul-lib-coverage": "^1.1.2", "istanbul-lib-hook": "^1.1.0", "istanbul-lib-instrument": "^1.9.2", "istanbul-lib-report": "^1.1.3", "istanbul-lib-source-maps": "^1.2.3", "istanbul-reports": "^1.1.4", "md5-hex": "^1.2.0", "merge-source-map": "^1.0.2", "micromatch": "^2.3.11", "mkdirp": "^0.5.0", "resolve-from": "^2.0.0", "rimraf": "^2.5.4", "signal-exit": "^3.0.1", "spawn-wrap": "^1.4.2", "test-exclude": "^4.2.0", "yargs": "^10.0.3", "yargs-parser": "^8.0.0"}, "dependencies": {"align-text": {"version": "0.1.4", "bundled": true, "dev": true, "optional": true, "requires": {"kind-of": "^3.0.2", "longest": "^1.0.1", "repeat-string": "^1.5.2"}}, "amdefine": {"version": "1.0.1", "bundled": true, "dev": true}, "ansi-regex": {"version": "2.1.1", "bundled": true, "dev": true}, "ansi-styles": {"version": "2.2.1", "bundled": true, "dev": true}, "append-transform": {"version": "0.4.0", "bundled": true, "dev": true, "requires": {"default-require-extensions": "^1.0.0"}}, "archy": {"version": "1.0.0", "bundled": true, "dev": true}, "arr-diff": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"arr-flatten": "^1.0.1"}}, "arr-flatten": {"version": "1.1.0", "bundled": true, "dev": true}, "array-unique": {"version": "0.2.1", "bundled": true, "dev": true}, "arrify": {"version": "1.0.1", "bundled": true, "dev": true}, "async": {"version": "1.5.2", "bundled": true, "dev": true}, "babel-code-frame": {"version": "6.26.0", "bundled": true, "dev": true, "requires": {"chalk": "^1.1.3", "esutils": "^2.0.2", "js-tokens": "^3.0.2"}}, "babel-generator": {"version": "6.26.1", "bundled": true, "dev": true, "requires": {"babel-messages": "^6.23.0", "babel-runtime": "^6.26.0", "babel-types": "^6.26.0", "detect-indent": "^4.0.0", "jsesc": "^1.3.0", "lodash": "^4.17.4", "source-map": "^0.5.7", "trim-right": "^1.0.1"}}, "babel-messages": {"version": "6.23.0", "bundled": true, "dev": true, "requires": {"babel-runtime": "^6.22.0"}}, "babel-runtime": {"version": "6.26.0", "bundled": true, "dev": true, "requires": {"core-js": "^2.4.0", "regenerator-runtime": "^0.11.0"}}, "babel-template": {"version": "6.26.0", "bundled": true, "dev": true, "requires": {"babel-runtime": "^6.26.0", "babel-traverse": "^6.26.0", "babel-types": "^6.26.0", "babylon": "^6.18.0", "lodash": "^4.17.4"}}, "babel-traverse": {"version": "6.26.0", "bundled": true, "dev": true, "requires": {"babel-code-frame": "^6.26.0", "babel-messages": "^6.23.0", "babel-runtime": "^6.26.0", "babel-types": "^6.26.0", "babylon": "^6.18.0", "debug": "^2.6.8", "globals": "^9.18.0", "invariant": "^2.2.2", "lodash": "^4.17.4"}}, "babel-types": {"version": "6.26.0", "bundled": true, "dev": true, "requires": {"babel-runtime": "^6.26.0", "esutils": "^2.0.2", "lodash": "^4.17.4", "to-fast-properties": "^1.0.3"}}, "babylon": {"version": "6.18.0", "bundled": true, "dev": true}, "balanced-match": {"version": "1.0.0", "bundled": true, "dev": true}, "brace-expansion": {"version": "1.1.11", "bundled": true, "dev": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "braces": {"version": "1.8.5", "bundled": true, "dev": true, "requires": {"expand-range": "^1.8.1", "preserve": "^0.2.0", "repeat-element": "^1.1.2"}}, "builtin-modules": {"version": "1.1.1", "bundled": true, "dev": true}, "caching-transform": {"version": "1.0.1", "bundled": true, "dev": true, "requires": {"md5-hex": "^1.2.0", "mkdirp": "^0.5.1", "write-file-atomic": "^1.1.4"}}, "camelcase": {"version": "1.2.1", "bundled": true, "dev": true, "optional": true}, "center-align": {"version": "0.1.3", "bundled": true, "dev": true, "optional": true, "requires": {"align-text": "^0.1.3", "lazy-cache": "^1.0.3"}}, "chalk": {"version": "1.1.3", "bundled": true, "dev": true, "requires": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}}, "cliui": {"version": "2.1.0", "bundled": true, "dev": true, "optional": true, "requires": {"center-align": "^0.1.1", "right-align": "^0.1.1", "wordwrap": "0.0.2"}, "dependencies": {"wordwrap": {"version": "0.0.2", "bundled": true, "dev": true, "optional": true}}}, "code-point-at": {"version": "1.1.0", "bundled": true, "dev": true}, "commondir": {"version": "1.0.1", "bundled": true, "dev": true}, "concat-map": {"version": "0.0.1", "bundled": true, "dev": true}, "convert-source-map": {"version": "1.5.1", "bundled": true, "dev": true}, "core-js": {"version": "2.5.3", "bundled": true, "dev": true}, "cross-spawn": {"version": "4.0.2", "bundled": true, "dev": true, "requires": {"lru-cache": "^4.0.1", "which": "^1.2.9"}}, "debug": {"version": "2.6.9", "bundled": true, "dev": true, "requires": {"ms": "2.0.0"}}, "debug-log": {"version": "1.0.1", "bundled": true, "dev": true}, "decamelize": {"version": "1.2.0", "bundled": true, "dev": true}, "default-require-extensions": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"strip-bom": "^2.0.0"}}, "detect-indent": {"version": "4.0.0", "bundled": true, "dev": true, "requires": {"repeating": "^2.0.0"}}, "error-ex": {"version": "1.3.1", "bundled": true, "dev": true, "requires": {"is-arrayish": "^0.2.1"}}, "escape-string-regexp": {"version": "1.0.5", "bundled": true, "dev": true}, "esutils": {"version": "2.0.2", "bundled": true, "dev": true}, "execa": {"version": "0.7.0", "bundled": true, "dev": true, "requires": {"cross-spawn": "^5.0.1", "get-stream": "^3.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "dependencies": {"cross-spawn": {"version": "5.1.0", "bundled": true, "dev": true, "requires": {"lru-cache": "^4.0.1", "shebang-command": "^1.2.0", "which": "^1.2.9"}}}}, "expand-brackets": {"version": "0.1.5", "bundled": true, "dev": true, "requires": {"is-posix-bracket": "^0.1.0"}}, "expand-range": {"version": "1.8.2", "bundled": true, "dev": true, "requires": {"fill-range": "^2.1.0"}}, "extglob": {"version": "0.3.2", "bundled": true, "dev": true, "requires": {"is-extglob": "^1.0.0"}}, "filename-regex": {"version": "2.0.1", "bundled": true, "dev": true}, "fill-range": {"version": "2.2.3", "bundled": true, "dev": true, "requires": {"is-number": "^2.1.0", "isobject": "^2.0.0", "randomatic": "^1.1.3", "repeat-element": "^1.1.2", "repeat-string": "^1.5.2"}}, "find-cache-dir": {"version": "0.1.1", "bundled": true, "dev": true, "requires": {"commondir": "^1.0.1", "mkdirp": "^0.5.1", "pkg-dir": "^1.0.0"}}, "find-up": {"version": "2.1.0", "bundled": true, "dev": true, "requires": {"locate-path": "^2.0.0"}}, "for-in": {"version": "1.0.2", "bundled": true, "dev": true}, "for-own": {"version": "0.1.5", "bundled": true, "dev": true, "requires": {"for-in": "^1.0.1"}}, "foreground-child": {"version": "1.5.6", "bundled": true, "dev": true, "requires": {"cross-spawn": "^4", "signal-exit": "^3.0.0"}}, "fs.realpath": {"version": "1.0.0", "bundled": true, "dev": true}, "get-caller-file": {"version": "1.0.2", "bundled": true, "dev": true}, "get-stream": {"version": "3.0.0", "bundled": true, "dev": true}, "glob": {"version": "7.1.2", "bundled": true, "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "glob-base": {"version": "0.3.0", "bundled": true, "dev": true, "requires": {"glob-parent": "^2.0.0", "is-glob": "^2.0.0"}}, "glob-parent": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"is-glob": "^2.0.0"}}, "globals": {"version": "9.18.0", "bundled": true, "dev": true}, "graceful-fs": {"version": "4.1.11", "bundled": true, "dev": true}, "handlebars": {"version": "4.0.11", "bundled": true, "dev": true, "requires": {"async": "^1.4.0", "optimist": "^0.6.1", "source-map": "^0.4.4", "uglify-js": "^2.6"}, "dependencies": {"source-map": {"version": "0.4.4", "bundled": true, "dev": true, "requires": {"amdefine": ">=0.0.4"}}}}, "has-ansi": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"ansi-regex": "^2.0.0"}}, "has-flag": {"version": "1.0.0", "bundled": true, "dev": true}, "hosted-git-info": {"version": "2.5.0", "bundled": true, "dev": true}, "imurmurhash": {"version": "0.1.4", "bundled": true, "dev": true}, "inflight": {"version": "1.0.6", "bundled": true, "dev": true, "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.3", "bundled": true, "dev": true}, "invariant": {"version": "2.2.2", "bundled": true, "dev": true, "requires": {"loose-envify": "^1.0.0"}}, "invert-kv": {"version": "1.0.0", "bundled": true, "dev": true}, "is-arrayish": {"version": "0.2.1", "bundled": true, "dev": true}, "is-buffer": {"version": "1.1.6", "bundled": true, "dev": true}, "is-builtin-module": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"builtin-modules": "^1.0.0"}}, "is-dotfile": {"version": "1.0.3", "bundled": true, "dev": true}, "is-equal-shallow": {"version": "0.1.3", "bundled": true, "dev": true, "requires": {"is-primitive": "^2.0.0"}}, "is-extendable": {"version": "0.1.1", "bundled": true, "dev": true}, "is-extglob": {"version": "1.0.0", "bundled": true, "dev": true}, "is-finite": {"version": "1.0.2", "bundled": true, "dev": true, "requires": {"number-is-nan": "^1.0.0"}}, "is-fullwidth-code-point": {"version": "2.0.0", "bundled": true, "dev": true}, "is-glob": {"version": "2.0.1", "bundled": true, "dev": true, "requires": {"is-extglob": "^1.0.0"}}, "is-number": {"version": "2.1.0", "bundled": true, "dev": true, "requires": {"kind-of": "^3.0.2"}}, "is-posix-bracket": {"version": "0.1.1", "bundled": true, "dev": true}, "is-primitive": {"version": "2.0.0", "bundled": true, "dev": true}, "is-stream": {"version": "1.1.0", "bundled": true, "dev": true}, "is-utf8": {"version": "0.2.1", "bundled": true, "dev": true}, "isarray": {"version": "1.0.0", "bundled": true, "dev": true}, "isexe": {"version": "2.0.0", "bundled": true, "dev": true}, "isobject": {"version": "2.1.0", "bundled": true, "dev": true, "requires": {"isarray": "1.0.0"}}, "istanbul-lib-coverage": {"version": "1.1.2", "bundled": true, "dev": true}, "istanbul-lib-hook": {"version": "1.1.0", "bundled": true, "dev": true, "requires": {"append-transform": "^0.4.0"}}, "istanbul-lib-instrument": {"version": "1.9.2", "bundled": true, "dev": true, "requires": {"babel-generator": "^6.18.0", "babel-template": "^6.16.0", "babel-traverse": "^6.18.0", "babel-types": "^6.18.0", "babylon": "^6.18.0", "istanbul-lib-coverage": "^1.1.2", "semver": "^5.3.0"}}, "istanbul-lib-report": {"version": "1.1.3", "bundled": true, "dev": true, "requires": {"istanbul-lib-coverage": "^1.1.2", "mkdirp": "^0.5.1", "path-parse": "^1.0.5", "supports-color": "^3.1.2"}, "dependencies": {"supports-color": {"version": "3.2.3", "bundled": true, "dev": true, "requires": {"has-flag": "^1.0.0"}}}}, "istanbul-lib-source-maps": {"version": "1.2.3", "bundled": true, "dev": true, "requires": {"debug": "^3.1.0", "istanbul-lib-coverage": "^1.1.2", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "source-map": "^0.5.3"}, "dependencies": {"debug": {"version": "3.1.0", "bundled": true, "dev": true, "requires": {"ms": "2.0.0"}}}}, "istanbul-reports": {"version": "1.1.4", "bundled": true, "dev": true, "requires": {"handlebars": "^4.0.3"}}, "js-tokens": {"version": "3.0.2", "bundled": true, "dev": true}, "jsesc": {"version": "1.3.0", "bundled": true, "dev": true}, "kind-of": {"version": "3.2.2", "bundled": true, "dev": true, "requires": {"is-buffer": "^1.1.5"}}, "lazy-cache": {"version": "1.0.4", "bundled": true, "dev": true, "optional": true}, "lcid": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"invert-kv": "^1.0.0"}}, "load-json-file": {"version": "1.1.0", "bundled": true, "dev": true, "requires": {"graceful-fs": "^4.1.2", "parse-json": "^2.2.0", "pify": "^2.0.0", "pinkie-promise": "^2.0.0", "strip-bom": "^2.0.0"}}, "locate-path": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"p-locate": "^2.0.0", "path-exists": "^3.0.0"}, "dependencies": {"path-exists": {"version": "3.0.0", "bundled": true, "dev": true}}}, "lodash": {"version": "4.17.5", "bundled": true, "dev": true}, "longest": {"version": "1.0.1", "bundled": true, "dev": true, "optional": true}, "loose-envify": {"version": "1.3.1", "bundled": true, "dev": true, "requires": {"js-tokens": "^3.0.0"}}, "lru-cache": {"version": "4.1.1", "bundled": true, "dev": true, "requires": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "md5-hex": {"version": "1.3.0", "bundled": true, "dev": true, "requires": {"md5-o-matic": "^0.1.1"}}, "md5-o-matic": {"version": "0.1.1", "bundled": true, "dev": true}, "mem": {"version": "1.1.0", "bundled": true, "dev": true, "requires": {"mimic-fn": "^1.0.0"}}, "merge-source-map": {"version": "1.1.0", "bundled": true, "dev": true, "requires": {"source-map": "^0.6.1"}, "dependencies": {"source-map": {"version": "0.6.1", "bundled": true, "dev": true}}}, "micromatch": {"version": "2.3.11", "bundled": true, "dev": true, "requires": {"arr-diff": "^2.0.0", "array-unique": "^0.2.1", "braces": "^1.8.2", "expand-brackets": "^0.1.4", "extglob": "^0.3.1", "filename-regex": "^2.0.0", "is-extglob": "^1.0.0", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "normalize-path": "^2.0.1", "object.omit": "^2.0.0", "parse-glob": "^3.0.4", "regex-cache": "^0.4.2"}}, "mimic-fn": {"version": "1.2.0", "bundled": true, "dev": true}, "minimatch": {"version": "3.0.4", "bundled": true, "dev": true, "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "0.0.8", "bundled": true, "dev": true}, "mkdirp": {"version": "0.5.1", "bundled": true, "dev": true, "requires": {"minimist": "0.0.8"}}, "ms": {"version": "2.0.0", "bundled": true, "dev": true}, "normalize-package-data": {"version": "2.4.0", "bundled": true, "dev": true, "requires": {"hosted-git-info": "^2.1.4", "is-builtin-module": "^1.0.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "normalize-path": {"version": "2.1.1", "bundled": true, "dev": true, "requires": {"remove-trailing-separator": "^1.0.1"}}, "npm-run-path": {"version": "2.0.2", "bundled": true, "dev": true, "requires": {"path-key": "^2.0.0"}}, "number-is-nan": {"version": "1.0.1", "bundled": true, "dev": true}, "object-assign": {"version": "4.1.1", "bundled": true, "dev": true}, "object.omit": {"version": "2.0.1", "bundled": true, "dev": true, "requires": {"for-own": "^0.1.4", "is-extendable": "^0.1.1"}}, "once": {"version": "1.4.0", "bundled": true, "dev": true, "requires": {"wrappy": "1"}}, "optimist": {"version": "0.6.1", "bundled": true, "dev": true, "requires": {"minimist": "~0.0.1", "wordwrap": "~0.0.2"}}, "os-homedir": {"version": "1.0.2", "bundled": true, "dev": true}, "os-locale": {"version": "2.1.0", "bundled": true, "dev": true, "requires": {"execa": "^0.7.0", "lcid": "^1.0.0", "mem": "^1.1.0"}}, "p-finally": {"version": "1.0.0", "bundled": true, "dev": true}, "p-limit": {"version": "1.2.0", "bundled": true, "dev": true, "requires": {"p-try": "^1.0.0"}}, "p-locate": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"p-limit": "^1.1.0"}}, "p-try": {"version": "1.0.0", "bundled": true, "dev": true}, "parse-glob": {"version": "3.0.4", "bundled": true, "dev": true, "requires": {"glob-base": "^0.3.0", "is-dotfile": "^1.0.0", "is-extglob": "^1.0.0", "is-glob": "^2.0.0"}}, "parse-json": {"version": "2.2.0", "bundled": true, "dev": true, "requires": {"error-ex": "^1.2.0"}}, "path-exists": {"version": "2.1.0", "bundled": true, "dev": true, "requires": {"pinkie-promise": "^2.0.0"}}, "path-is-absolute": {"version": "1.0.1", "bundled": true, "dev": true}, "path-key": {"version": "2.0.1", "bundled": true, "dev": true}, "path-parse": {"version": "1.0.5", "bundled": true, "dev": true}, "path-type": {"version": "1.1.0", "bundled": true, "dev": true, "requires": {"graceful-fs": "^4.1.2", "pify": "^2.0.0", "pinkie-promise": "^2.0.0"}}, "pify": {"version": "2.3.0", "bundled": true, "dev": true}, "pinkie": {"version": "2.0.4", "bundled": true, "dev": true}, "pinkie-promise": {"version": "2.0.1", "bundled": true, "dev": true, "requires": {"pinkie": "^2.0.0"}}, "pkg-dir": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"find-up": "^1.0.0"}, "dependencies": {"find-up": {"version": "1.1.2", "bundled": true, "dev": true, "requires": {"path-exists": "^2.0.0", "pinkie-promise": "^2.0.0"}}}}, "preserve": {"version": "0.2.0", "bundled": true, "dev": true}, "pseudomap": {"version": "1.0.2", "bundled": true, "dev": true}, "randomatic": {"version": "1.1.7", "bundled": true, "dev": true, "requires": {"is-number": "^3.0.0", "kind-of": "^4.0.0"}, "dependencies": {"is-number": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "bundled": true, "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "kind-of": {"version": "4.0.0", "bundled": true, "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "read-pkg": {"version": "1.1.0", "bundled": true, "dev": true, "requires": {"load-json-file": "^1.0.0", "normalize-package-data": "^2.3.2", "path-type": "^1.0.0"}}, "read-pkg-up": {"version": "1.0.1", "bundled": true, "dev": true, "requires": {"find-up": "^1.0.0", "read-pkg": "^1.0.0"}, "dependencies": {"find-up": {"version": "1.1.2", "bundled": true, "dev": true, "requires": {"path-exists": "^2.0.0", "pinkie-promise": "^2.0.0"}}}}, "regenerator-runtime": {"version": "0.11.1", "bundled": true, "dev": true}, "regex-cache": {"version": "0.4.4", "bundled": true, "dev": true, "requires": {"is-equal-shallow": "^0.1.3"}}, "remove-trailing-separator": {"version": "1.1.0", "bundled": true, "dev": true}, "repeat-element": {"version": "1.1.2", "bundled": true, "dev": true}, "repeat-string": {"version": "1.6.1", "bundled": true, "dev": true}, "repeating": {"version": "2.0.1", "bundled": true, "dev": true, "requires": {"is-finite": "^1.0.0"}}, "require-directory": {"version": "2.1.1", "bundled": true, "dev": true}, "require-main-filename": {"version": "1.0.1", "bundled": true, "dev": true}, "resolve-from": {"version": "2.0.0", "bundled": true, "dev": true}, "right-align": {"version": "0.1.3", "bundled": true, "dev": true, "optional": true, "requires": {"align-text": "^0.1.1"}}, "rimraf": {"version": "2.6.2", "bundled": true, "dev": true, "requires": {"glob": "^7.0.5"}}, "semver": {"version": "5.5.0", "bundled": true, "dev": true}, "set-blocking": {"version": "2.0.0", "bundled": true, "dev": true}, "shebang-command": {"version": "1.2.0", "bundled": true, "dev": true, "requires": {"shebang-regex": "^1.0.0"}}, "shebang-regex": {"version": "1.0.0", "bundled": true, "dev": true}, "signal-exit": {"version": "3.0.2", "bundled": true, "dev": true}, "slide": {"version": "1.1.6", "bundled": true, "dev": true}, "source-map": {"version": "0.5.7", "bundled": true, "dev": true}, "spawn-wrap": {"version": "1.4.2", "bundled": true, "dev": true, "requires": {"foreground-child": "^1.5.6", "mkdirp": "^0.5.0", "os-homedir": "^1.0.1", "rimraf": "^2.6.2", "signal-exit": "^3.0.2", "which": "^1.3.0"}}, "spdx-correct": {"version": "1.0.2", "bundled": true, "dev": true, "requires": {"spdx-license-ids": "^1.0.2"}}, "spdx-expression-parse": {"version": "1.0.4", "bundled": true, "dev": true}, "spdx-license-ids": {"version": "1.2.2", "bundled": true, "dev": true}, "string-width": {"version": "2.1.1", "bundled": true, "dev": true, "requires": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}, "dependencies": {"ansi-regex": {"version": "3.0.0", "bundled": true, "dev": true}, "strip-ansi": {"version": "4.0.0", "bundled": true, "dev": true, "requires": {"ansi-regex": "^3.0.0"}}}}, "strip-ansi": {"version": "3.0.1", "bundled": true, "dev": true, "requires": {"ansi-regex": "^2.0.0"}}, "strip-bom": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"is-utf8": "^0.2.0"}}, "strip-eof": {"version": "1.0.0", "bundled": true, "dev": true}, "supports-color": {"version": "2.0.0", "bundled": true, "dev": true}, "test-exclude": {"version": "4.2.0", "bundled": true, "dev": true, "requires": {"arrify": "^1.0.1", "micromatch": "^2.3.11", "object-assign": "^4.1.0", "read-pkg-up": "^1.0.1", "require-main-filename": "^1.0.1"}}, "to-fast-properties": {"version": "1.0.3", "bundled": true, "dev": true}, "trim-right": {"version": "1.0.1", "bundled": true, "dev": true}, "uglify-js": {"version": "2.8.29", "bundled": true, "dev": true, "optional": true, "requires": {"source-map": "~0.5.1", "uglify-to-browserify": "~1.0.0", "yargs": "~3.10.0"}, "dependencies": {"yargs": {"version": "3.10.0", "bundled": true, "dev": true, "optional": true, "requires": {"camelcase": "^1.0.2", "cliui": "^2.1.0", "decamelize": "^1.0.0", "window-size": "0.1.0"}}}}, "uglify-to-browserify": {"version": "1.0.2", "bundled": true, "dev": true, "optional": true}, "validate-npm-package-license": {"version": "3.0.1", "bundled": true, "dev": true, "requires": {"spdx-correct": "~1.0.0", "spdx-expression-parse": "~1.0.0"}}, "which": {"version": "1.3.0", "bundled": true, "dev": true, "requires": {"isexe": "^2.0.0"}}, "which-module": {"version": "2.0.0", "bundled": true, "dev": true}, "window-size": {"version": "0.1.0", "bundled": true, "dev": true, "optional": true}, "wordwrap": {"version": "0.0.3", "bundled": true, "dev": true}, "wrap-ansi": {"version": "2.1.0", "bundled": true, "dev": true, "requires": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1"}, "dependencies": {"is-fullwidth-code-point": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"number-is-nan": "^1.0.0"}}, "string-width": {"version": "1.0.2", "bundled": true, "dev": true, "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}}}}, "wrappy": {"version": "1.0.2", "bundled": true, "dev": true}, "write-file-atomic": {"version": "1.3.4", "bundled": true, "dev": true, "requires": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "slide": "^1.1.5"}}, "y18n": {"version": "3.2.1", "bundled": true, "dev": true}, "yallist": {"version": "2.1.2", "bundled": true, "dev": true}, "yargs": {"version": "10.1.2", "bundled": true, "dev": true, "requires": {"cliui": "^4.0.0", "decamelize": "^1.1.1", "find-up": "^2.1.0", "get-caller-file": "^1.0.1", "os-locale": "^2.0.0", "require-directory": "^2.1.1", "require-main-filename": "^1.0.1", "set-blocking": "^2.0.0", "string-width": "^2.0.0", "which-module": "^2.0.0", "y18n": "^3.2.1", "yargs-parser": "^8.1.0"}, "dependencies": {"ansi-regex": {"version": "3.0.0", "bundled": true, "dev": true}, "cliui": {"version": "4.0.0", "bundled": true, "dev": true, "requires": {"string-width": "^2.1.1", "strip-ansi": "^4.0.0", "wrap-ansi": "^2.0.0"}}, "strip-ansi": {"version": "4.0.0", "bundled": true, "dev": true, "requires": {"ansi-regex": "^3.0.0"}}}}, "yargs-parser": {"version": "8.1.0", "bundled": true, "dev": true, "requires": {"camelcase": "^4.1.0"}, "dependencies": {"camelcase": {"version": "4.1.0", "bundled": true, "dev": true}}}}}, "oauth-sign": {"version": "0.9.0", "resolved": "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.9.0.tgz", "integrity": "sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ=="}, "object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="}, "object-component": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/object-component/-/object-component-0.0.3.tgz", "integrity": "sha1-8MaapQ78lbhmwYb0AKM3acsvEpE="}, "object-copy": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/object-copy/-/object-copy-0.1.0.tgz", "integrity": "sha1-fn2Fi3gb18mRpBupde04EnVOmYw=", "requires": {"copy-descriptor": "^0.1.0", "define-property": "^0.2.5", "kind-of": "^3.0.3"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "is-buffer": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w=="}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "object-filter-sequence": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/object-filter-sequence/-/object-filter-sequence-1.0.0.tgz", "integrity": "sha512-CsubGNxhIEChNY4cXYuA6KXafztzHqzLLZ/y3Kasf3A+sa3lL9thq3z+7o0pZqzEinjXT6lXDPAfVWI59dUyzQ=="}, "object-hash": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/object-hash/-/object-hash-0.3.0.tgz", "integrity": "sha1-VIII5Ds2pE5NowutbFasU7iF50Q="}, "object-identity-map": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/object-identity-map/-/object-identity-map-1.0.2.tgz", "integrity": "sha512-a2XZDGyYTngvGS67kWnqVdpoaJWsY7C1GhPJvejWAFCsUioTAaiTu8oBad7c6cI4McZxr4CmvnZeycK05iav5A==", "requires": {"object.entries": "^1.1.0"}}, "object-inspect": {"version": "1.8.0", "resolved": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.8.0.tgz", "integrity": "sha512-jLdtEOB112fORuypAyl/50VRVIBIdVQOSUUGQHzJ4xBSbit81zRarz7GThkEFZy1RceYrWYcPcBFPQwHyAc1gA=="}, "object-keys": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz", "integrity": "sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA=="}, "object-visit": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/object-visit/-/object-visit-1.0.1.tgz", "integrity": "sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=", "requires": {"isobject": "^3.0.0"}}, "object.assign": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/object.assign/-/object.assign-4.1.0.tgz", "integrity": "sha512-exHJeq6kBKj58mqGyTQ9DFvrZC/eR6OwxzoM9YRoGBqrXYonaFyGiFMuc9VZrXf7DarreEwMpurG3dd+CNyW5w==", "requires": {"define-properties": "^1.1.2", "function-bind": "^1.1.1", "has-symbols": "^1.0.0", "object-keys": "^1.0.11"}}, "object.entries": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/object.entries/-/object.entries-1.1.2.tgz", "integrity": "sha512-BQdB9qKmb/HyNdMNWVr7O3+z5MUIx3aiegEIJqjMBbBf0YT9RRxTJSim4mzFqtyr7PDAHigq0N9dO0m0tRakQA==", "requires": {"define-properties": "^1.1.3", "es-abstract": "^1.17.5", "has": "^1.0.3"}}, "object.pick": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/object.pick/-/object.pick-1.3.0.tgz", "integrity": "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=", "requires": {"isobject": "^3.0.1"}}, "on-finished": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/on-finished/-/on-finished-2.3.0.tgz", "integrity": "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=", "requires": {"ee-first": "1.1.1"}}, "on-headers": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/on-headers/-/on-headers-1.0.2.tgz", "integrity": "sha512-pZA<PERSON>+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA=="}, "once": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "requires": {"wrappy": "1"}}, "one-time": {"version": "0.0.4", "resolved": "https://registry.npmjs.org/one-time/-/one-time-0.0.4.tgz", "integrity": "sha1-+M33eISCb+Tf+T46nMN7HkSAdC4="}, "opencollective-postinstall": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/opencollective-postinstall/-/opencollective-postinstall-2.0.3.tgz", "integrity": "sha512-8AV/sCtuzUeTo8gQK5qDZzARrulB3egtLzFgteqB2tcT4Mw7B8Kt7JcDHmltjz6FOAHsvTevk70gZEbhM4ZS9Q=="}, "optional-js": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/optional-js/-/optional-js-2.3.0.tgz", "integrity": "sha512-B0LLi+Vg+eko++0z/b8zIv57kp7HKEzaPJo7LowJXMUKYdf+3XJGu/cw03h/JhIOsLnP+cG5QnTHAuicjA5fMw=="}, "original-url": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/original-url/-/original-url-1.2.3.tgz", "integrity": "sha512-BYm+pKYLtS4mVe/mgT3YKGtWV5HzN/XKiaIu1aK4rsxyjuHeTW9N+xVBEpJcY1onB3nccfH0RbzUEoimMqFUHQ==", "requires": {"forwarded-parse": "^2.1.0"}}, "otp": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/otp/-/otp-0.1.3.tgz", "integrity": "sha1-wle/JdL5Anr3esUiabPBQmjSvWs=", "requires": {"thirty-two": "^0.0.2"}}, "p-limit": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz", "integrity": "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==", "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz", "integrity": "sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==", "requires": {"p-limit": "^2.2.0"}}, "p-try": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz", "integrity": "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ=="}, "packet-reader": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/packet-reader/-/packet-reader-0.3.1.tgz", "integrity": "sha1-zWLmCvjX/qinBexP+ZCHHEaHHyc="}, "parse-json": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/parse-json/-/parse-json-5.1.0.tgz", "integrity": "sha512-+mi/lmVVNKFNVyLXV31ERiy2CY5E1/F6QtJFEzoChPRwwngMNXRDQ9GJ5WdE2Z2P4AujsOi0/+2qHID68KwfIQ==", "requires": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}}, "parseqs": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/parseqs/-/parseqs-0.0.5.tgz", "integrity": "sha1-1SCKNzjkZ2bikbouoXNoSSGouJ0=", "requires": {"better-assert": "~1.0.0"}}, "parseuri": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/parseuri/-/parseuri-0.0.5.tgz", "integrity": "sha1-gCBKUNTbt3m/3G6+J3jZDkvOMgo=", "requires": {"better-assert": "~1.0.0"}}, "parseurl": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz", "integrity": "sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ=="}, "pascalcase": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/pascalcase/-/pascalcase-0.1.1.tgz", "integrity": "sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ="}, "path-dirname": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/path-dirname/-/path-dirname-1.0.2.tgz", "integrity": "sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA="}, "path-exists": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz", "integrity": "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w=="}, "path-is-absolute": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="}, "path-loader": {"version": "1.0.10", "resolved": "https://registry.npmjs.org/path-loader/-/path-loader-1.0.10.tgz", "integrity": "sha512-CMP0v6S6z8PHeJ6NFVyVJm6WyJjIwFvyz2b0n2/4bKdS/0uZa/9sKUlYZzubrn3zuDRU0zIuEDX9DZYQ2ZI8TA==", "requires": {"native-promise-only": "^0.8.1", "superagent": "^3.8.3"}, "dependencies": {"debug": {"version": "3.2.6", "resolved": "https://registry.npmjs.org/debug/-/debug-3.2.6.tgz", "integrity": "sha512-mel+jf7nrtEl5Pn1Qx46zARXKDpBbvzezse7p7LqINmdoIk8PYP5SySaxEmYv6TZ0JyEKA1hsCId6DIhgITtWQ==", "requires": {"ms": "^2.1.1"}}, "readable-stream": {"version": "2.3.7", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.7.tgz", "integrity": "sha512-<PERSON>bho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw==", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==", "requires": {"safe-buffer": "~5.1.0"}}, "superagent": {"version": "3.8.3", "resolved": "https://registry.npmjs.org/superagent/-/superagent-3.8.3.tgz", "integrity": "sha512-GLQtLMCoEIK4eDv6OGtkOoSMt3D+oq0y3dsxMuYuDvaNUvuT8eFBuLmfR0iYYzHC1e8hpzC6ZsxbuP6DIalMFA==", "requires": {"component-emitter": "^1.2.0", "cookiejar": "^2.1.0", "debug": "^3.1.0", "extend": "^3.0.0", "form-data": "^2.3.1", "formidable": "^1.2.0", "methods": "^1.1.1", "mime": "^1.4.1", "qs": "^6.5.1", "readable-stream": "^2.3.5"}}}}, "path-parse": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.6.tgz", "integrity": "sha512-GSmOT2EbHrINBf9SR7CDELwlJ8AENk3Qn7OikK4nFYAu3Ote2+JYNVvkpAEQm3/TLNEJFD/xZJjzyxg3KBWOzw=="}, "path-to-regexp": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.7.tgz", "integrity": "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w="}, "pathval": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/pathval/-/pathval-1.1.0.tgz", "integrity": "sha1-uULm1L3mUwBe9rcTYd74cn0GReA="}, "pend": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/pend/-/pend-1.2.0.tgz", "integrity": "sha1-elfrVQpng/kRUzH89GY9XI4AelA="}, "performance-now": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz", "integrity": "sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns="}, "pg": {"version": "6.4.2", "resolved": "https://registry.npmjs.org/pg/-/pg-6.4.2.tgz", "integrity": "sha1-w2QBEGDqx6UHoq4GPrhX7OkQ4n8=", "requires": {"buffer-writer": "1.0.1", "js-string-escape": "1.0.1", "packet-reader": "0.3.1", "pg-connection-string": "0.1.3", "pg-pool": "1.*", "pg-types": "1.*", "pgpass": "1.*", "semver": "4.3.2"}, "dependencies": {"semver": {"version": "4.3.2", "resolved": "https://registry.npmjs.org/semver/-/semver-4.3.2.tgz", "integrity": "sha1-x6BxWKgL7dBSNVt3DYLWZA+AO+c="}}}, "pg-connection-string": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/pg-connection-string/-/pg-connection-string-0.1.3.tgz", "integrity": "sha1-2hhHsglA5C7hSSvq9l1J2RskXfc="}, "pg-hstore": {"version": "2.3.2", "resolved": "https://registry.npmjs.org/pg-hstore/-/pg-hstore-2.3.2.tgz", "integrity": "sha1-9+8FPnubiSrphq8vfL6GQy388k8=", "requires": {"underscore": "^1.7.0"}}, "pg-int8": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/pg-int8/-/pg-int8-1.0.1.tgz", "integrity": "sha512-WCtabS6t3c8SkpDBUlb1kjOs7l66xsGdKpIPZsg4wR+B3+u9UAum2odSsF9tnvxg80h4ZxLWMy4pRjOsFIqQpw=="}, "pg-pool": {"version": "1.8.0", "resolved": "https://registry.npmjs.org/pg-pool/-/pg-pool-1.8.0.tgz", "integrity": "sha1-9+xzgkw3oD8Hb1G/33DjQBR8Tzc=", "requires": {"generic-pool": "2.4.3", "object-assign": "4.1.0"}, "dependencies": {"object-assign": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.0.tgz", "integrity": "sha1-ejs9DpgGPUP0wD8uiubNUahog6A="}}}, "pg-types": {"version": "1.13.0", "resolved": "https://registry.npmjs.org/pg-types/-/pg-types-1.13.0.tgz", "integrity": "sha512-lfKli0Gkl/+za/+b6lzENajczwZHc7D5kiUCZfgm914jipD2kIOIvEkAhZ8GrW3/TUoP9w8FHjwpPObBye5KQQ==", "requires": {"pg-int8": "1.0.1", "postgres-array": "~1.0.0", "postgres-bytea": "~1.0.0", "postgres-date": "~1.0.0", "postgres-interval": "^1.1.0"}}, "pgpass": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/pgpass/-/pgpass-1.0.2.tgz", "integrity": "sha1-Knu0G2BltnkH6R2hsHwYR8h3swY=", "requires": {"split": "^1.0.0"}}, "pidusage": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/pidusage/-/pidusage-1.2.0.tgz", "integrity": "sha512-OGo+iSOk44HRJ8q15AyG570UYxcm5u+R99DI8Khu8P3tKGkVu5EZX4ywHglWSTMNNXQ274oeGpYrvFEhDIFGPg=="}, "pipeworks": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/pipeworks/-/pipeworks-1.3.1.tgz", "integrity": "sha1-+ENvhWXtHZe/OoBjKlOXv9NTOF8="}, "platform": {"version": "1.3.5", "resolved": "https://registry.npmjs.org/platform/-/platform-1.3.5.tgz", "integrity": "sha512-TuvHS8AOIZNAlE77WUDiR4rySV/VMptyMfcfeoMgs4P8apaZM3JrnbzBiixKUv+XR6i+BXrQh8WAnjaSPFO65Q=="}, "pm2": {"version": "2.10.1", "resolved": "https://registry.npmjs.org/pm2/-/pm2-2.10.1.tgz", "integrity": "sha512-l5U3Hh908TMw7qyOVAlCCoRlgonpnEw5pr2YnKT8+8fuay9tAysPTccU+SLPDjI6hQ9Q03p4lfNOtJUczzTrPA==", "requires": {"async": "^2.5", "blessed": "^0.1.81", "chalk": "^1.1", "chokidar": "^2", "cli-table-redemption": "^1.0.0", "commander": "2.13.0", "cron": "^1.3", "debug": "^3.0", "eventemitter2": "1.0.5", "fclone": "1.0.11", "gkt": "https://tgz.pm2.io/gkt-1.0.0.tgz", "mkdirp": "0.5.1", "moment": "^2.19", "needle": "^2.1.0", "nssocket": "0.6.0", "pidusage": "^1.2.0", "pm2-axon": "3.1.0", "pm2-axon-rpc": "0.5.0", "pm2-deploy": "^0.3.9", "pm2-multimeter": "^0.1.2", "pmx": "^1.6", "promptly": "2.2.0", "semver": "^5.3", "shelljs": "0.7.8", "source-map-support": "^0.5", "sprintf-js": "1.1.1", "v8-compile-cache": "^1.1.0", "vizion": "^0.2", "yamljs": "^0.3.0"}, "dependencies": {"ansi-styles": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-2.2.1.tgz", "integrity": "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4="}, "chalk": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/chalk/-/chalk-1.1.3.tgz", "integrity": "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=", "requires": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}}, "commander": {"version": "2.13.0", "resolved": "https://registry.npmjs.org/commander/-/commander-2.13.0.tgz", "integrity": "sha512-MVuS359B+YzaWqjCL/c+22gfryv+mCBPHAv3zyVI2GN8EY6IRP8VwtasXn8jyyhvvq84R4ImN1OKRtcbIasjYA=="}, "debug": {"version": "3.2.6", "resolved": "https://registry.npmjs.org/debug/-/debug-3.2.6.tgz", "integrity": "sha512-mel+jf7nrtEl5Pn1Qx46zARXKDpBbvzezse7p7LqINmdoIk8PYP5SySaxEmYv6TZ0JyEKA1hsCId6DIhgITtWQ==", "requires": {"ms": "^2.1.1"}}, "minimist": {"version": "0.0.8", "resolved": "https://registry.npmjs.org/minimist/-/minimist-0.0.8.tgz", "integrity": "sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0="}, "mkdirp": {"version": "0.5.1", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.1.tgz", "integrity": "sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM=", "requires": {"minimist": "0.0.8"}}, "semver": {"version": "5.7.1", "resolved": "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz", "integrity": "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ=="}, "sprintf-js": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.1.1.tgz", "integrity": "sha1-Nr54Mgr+WAH2zqPueLblqrlA6gw="}, "supports-color": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-2.0.0.tgz", "integrity": "sha1-U10EXOa2Nj+kARcIRimZXp3zJMc="}}}, "pm2-axon": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/pm2-axon/-/pm2-axon-3.1.0.tgz", "integrity": "sha512-5sBM+vHw0Cp2K9CJ9ZOYhKtNCCcgQ0eKOyFrSo5Jusbq9FfvuelsMG4WDaxkqosaQbf8N5YfyHhD7eOUcnm5rQ==", "requires": {"amp": "~0.3.1", "amp-message": "~0.1.1", "debug": "^3.0", "escape-regexp": "0.0.1"}, "dependencies": {"debug": {"version": "3.2.6", "resolved": "https://registry.npmjs.org/debug/-/debug-3.2.6.tgz", "integrity": "sha512-mel+jf7nrtEl5Pn1Qx46zARXKDpBbvzezse7p7LqINmdoIk8PYP5SySaxEmYv6TZ0JyEKA1hsCId6DIhgITtWQ==", "requires": {"ms": "^2.1.1"}}}}, "pm2-axon-rpc": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/pm2-axon-rpc/-/pm2-axon-rpc-0.5.0.tgz", "integrity": "sha512-jKiAlnIitx+TtJ1++jThmN49gM0Dte4gm27Kqu2xAUQn33Rh9+5lOOqShS5Xbp0RPZL42hKNEgaVVOSqm3sJCg==", "requires": {"debug": "^3.0", "fclone": "^1"}, "dependencies": {"debug": {"version": "3.2.6", "resolved": "https://registry.npmjs.org/debug/-/debug-3.2.6.tgz", "integrity": "sha512-mel+jf7nrtEl5Pn1Qx46zARXKDpBbvzezse7p7LqINmdoIk8PYP5SySaxEmYv6TZ0JyEKA1hsCId6DIhgITtWQ==", "requires": {"ms": "^2.1.1"}}}}, "pm2-deploy": {"version": "0.3.10", "resolved": "https://registry.npmjs.org/pm2-deploy/-/pm2-deploy-0.3.10.tgz", "integrity": "sha512-WagPKsX+LDCe8wLCL5nzu8RQvVUQ5GlFdJRVYCL0ogFnHfYRym91qNU4PkNSWSq11pdvG8la7DTjdW6FWXc8lw==", "requires": {"async": "^2.6", "tv4": "^1.3"}}, "pm2-multimeter": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/pm2-multimeter/-/pm2-multimeter-0.1.2.tgz", "integrity": "sha1-Gh5VFT1BoFU0zqI8/oYKuqDrSs4=", "requires": {"charm": "~0.1.1"}}, "pmx": {"version": "1.6.4", "resolved": "https://registry.npmjs.org/pmx/-/pmx-1.6.4.tgz", "integrity": "sha512-uk6REZHe8j3RhGlFUfyVwFrE6JFhZigTpMFs/4iYO4MzCqVTpNp1cED032oCc4R+m32wUITV/2RDmPX21T1LLg==", "requires": {"debug": "^3", "deep-metrics": "^0.0.1", "json-stringify-safe": "^5.0", "semver": "5.*", "vxx": "^1.2.0"}, "dependencies": {"debug": {"version": "3.2.6", "resolved": "https://registry.npmjs.org/debug/-/debug-3.2.6.tgz", "integrity": "sha512-mel+jf7nrtEl5Pn1Qx46zARXKDpBbvzezse7p7LqINmdoIk8PYP5SySaxEmYv6TZ0JyEKA1hsCId6DIhgITtWQ==", "requires": {"ms": "^2.1.1"}}, "semver": {"version": "5.7.1", "resolved": "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz", "integrity": "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ=="}}}, "posix-character-classes": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/posix-character-classes/-/posix-character-classes-0.1.1.tgz", "integrity": "sha1-AerA/jta9xoqbAL+q7jB/vfgDqs="}, "postgres-array": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/postgres-array/-/postgres-array-1.0.3.tgz", "integrity": "sha512-5wClXrAP0+78mcsNX3/ithQ5exKvCyK5lr5NEEEeGwwM6NJdQgzIJBVxLvRW+huFpX92F2QnZ5CcokH0VhK2qQ=="}, "postgres-bytea": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/postgres-bytea/-/postgres-bytea-1.0.0.tgz", "integrity": "sha1-AntTPAqokOJtFy1Hz5zOzFIazTU="}, "postgres-date": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/postgres-date/-/postgres-date-1.0.7.tgz", "integrity": "sha512-suDmjLVQg78nMK2UZ454hAG+OAW+HQPZ6n++TNDUX+L0+uUlLywnoxJKDou51Zm+zTCjrCl0Nq6J9C5hP9vK/Q=="}, "postgres-interval": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/postgres-interval/-/postgres-interval-1.2.0.tgz", "integrity": "sha512-9ZhXKM/rw350N1ovuWHbGxnGh/SNJ4cnxHiM0rxE4VN41wsg8P8zWn9hv/buK00RP4WvlOyr/RBDiptyxVbkZQ==", "requires": {"xtend": "^4.0.0"}}, "process-nextick-args": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "integrity": "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag=="}, "promptly": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/promptly/-/promptly-2.2.0.tgz", "integrity": "sha1-KhP6BjaIoqWYOxYf/wEIoH0m/HQ=", "requires": {"read": "^1.0.4"}}, "proxy-addr": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.6.tgz", "integrity": "sha512-dh/frvCBVmSsDYzw6n926jv974gddhkFPfiN8hPOi30Wax25QZyZEGveluCgliBnqmuM+UJmBErbAUFIoDbjOw==", "requires": {"forwarded": "~0.1.2", "ipaddr.js": "1.9.1"}}, "pseudomap": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/pseudomap/-/pseudomap-1.0.2.tgz", "integrity": "sha1-8FKijacOYYkX7wqKw0wa5aaChrM="}, "psl": {"version": "1.8.0", "resolved": "https://registry.npmjs.org/psl/-/psl-1.8.0.tgz", "integrity": "sha512-RIdOzyoavK+hA18OGGWDqUTsCLhtA7IcZ/6NCs4fFJaHBDab+pDDmDIByWFRQJq2Cd7r1OoQxBGKOaztq+hjIQ=="}, "pump": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/pump/-/pump-3.0.0.tgz", "integrity": "sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww==", "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "punycode": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/punycode/-/punycode-1.3.2.tgz", "integrity": "sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0="}, "qs": {"version": "6.5.1", "resolved": "https://registry.npmjs.org/qs/-/qs-6.5.1.tgz", "integrity": "sha512-eRzhrN1WSINYCDCbrz796z37LOe3m5tmW7RQf6oBntukAG1nmovJvhnwHHRMAfeoItc1m2Hk02WER2aQ/iqs+A=="}, "querystring": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/querystring/-/querystring-0.2.0.tgz", "integrity": "sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA="}, "random-poly-fill": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/random-poly-fill/-/random-poly-fill-1.0.1.tgz", "integrity": "sha512-bMOL0hLfrNs52+EHtIPIXxn2PxYwXb0qjnKruTjXiM/sKfYqj506aB2plFwWW1HN+ri724bAVVGparh4AtlJKw=="}, "random-string": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/random-string/-/random-string-0.2.0.tgz", "integrity": "sha1-pG5DdTUr7amg17DRntbTIezR2C0="}, "range-parser": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz", "integrity": "sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg=="}, "raw-body": {"version": "2.3.2", "resolved": "https://registry.npmjs.org/raw-body/-/raw-body-2.3.2.tgz", "integrity": "sha1-vNYMd9Prk83gBQKVw/N5OJvIj4k=", "requires": {"bytes": "3.0.0", "http-errors": "1.6.2", "iconv-lite": "0.4.19", "unpipe": "1.0.0"}, "dependencies": {"depd": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/depd/-/depd-1.1.1.tgz", "integrity": "sha1-V4O04cRZ8G+lyif5kfPQbnoxA1k="}, "http-errors": {"version": "1.6.2", "resolved": "https://registry.npmjs.org/http-errors/-/http-errors-1.6.2.tgz", "integrity": "sha1-CgAsyFcHGSp+eUbO7cERVfYOxzY=", "requires": {"depd": "1.1.1", "inherits": "2.0.3", "setprototypeof": "1.0.3", "statuses": ">= 1.3.1 < 2"}}, "inherits": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="}, "setprototypeof": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.0.3.tgz", "integrity": "sha1-ZlZ+NwQ+608E2RvWWMDL77VbjgQ="}}}, "read": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/read/-/read-1.0.7.tgz", "integrity": "sha1-s9oZvQUkMal2cdRKQmNK33ELQMQ=", "requires": {"mute-stream": "~0.0.4"}}, "read-pkg": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/read-pkg/-/read-pkg-5.2.0.tgz", "integrity": "sha512-Ug69mNOpfvKDAc2Q8DRpMjjzdtrnv9HcSMX+4VsZxD1aZ6ZzrIE7rlzXBtWTyhULSMKg076AW6WR5iZpD0JiOg==", "requires": {"@types/normalize-package-data": "^2.4.0", "normalize-package-data": "^2.5.0", "parse-json": "^5.0.0", "type-fest": "^0.6.0"}, "dependencies": {"type-fest": {"version": "0.6.0", "resolved": "https://registry.npmjs.org/type-fest/-/type-fest-0.6.0.tgz", "integrity": "sha512-q+MB8nYR1KDLrgr4G5yemftpMC7/QLqVndBmEEdqzmNj5dcFOO4Oo8qlwZE3ULT3+Zim1F8Kq4cBnikNhlCMlg=="}}}, "read-pkg-up": {"version": "7.0.1", "resolved": "https://registry.npmjs.org/read-pkg-up/-/read-pkg-up-7.0.1.tgz", "integrity": "sha512-zK0TB7Xd6JpCLmlLmufqykGE+/TlOePD6qKClNW7hHDKFh/J7/7gCWGR7joEQEW1bKq3a3yUZSObOoWLFQ4ohg==", "requires": {"find-up": "^4.1.0", "read-pkg": "^5.2.0", "type-fest": "^0.8.1"}}, "readable-stream": {"version": "3.6.0", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.0.tgz", "integrity": "sha512-BViHy7LKeTz4oNnkcLJ+lVSL6vpiFeX6/d3oSH8zCW7UxP2onchk+vTGB143xuFjHS3deTgkKoXXymXqymiIdA==", "requires": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}}, "readdirp": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/readdirp/-/readdirp-2.2.1.tgz", "integrity": "sha512-1JU/8q+VgFZyxwrJ+SVIOsh+KywWGpds3NTqikiKpDMZWScmAYyKIgqkO+ARvNWJfXeXR1zxz7aHF4u4CyH6vQ==", "requires": {"graceful-fs": "^4.1.11", "micromatch": "^3.1.10", "readable-stream": "^2.0.2"}, "dependencies": {"readable-stream": {"version": "2.3.7", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.7.tgz", "integrity": "sha512-<PERSON>bho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw==", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==", "requires": {"safe-buffer": "~5.1.0"}}}}, "rechoir": {"version": "0.6.2", "resolved": "https://registry.npmjs.org/rechoir/-/rechoir-0.6.2.tgz", "integrity": "sha1-hSBLVNuoLVdC4oyWdW70OvUOM4Q=", "requires": {"resolve": "^1.1.6"}}, "redact-secrets": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/redact-secrets/-/redact-secrets-1.0.0.tgz", "integrity": "sha1-YPHbVpJP6QogO6jMs5KDzbsNkHw=", "requires": {"is-secret": "^1.0.0", "traverse": "^0.6.6"}}, "redis": {"version": "2.8.0", "resolved": "https://registry.npmjs.org/redis/-/redis-2.8.0.tgz", "integrity": "sha512-M1OkonEQwtRmZv4tEWF2VgpG0JWJ8Fv1PhlgT5+B+uNq2cA3Rt1Yt/ryoR+vQNOQcIEgdCdfH0jr3bDpihAw1A==", "requires": {"double-ended-queue": "^2.1.0-0", "redis-commands": "^1.2.0", "redis-parser": "^2.6.0"}}, "redis-commands": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/redis-commands/-/redis-commands-1.6.0.tgz", "integrity": "sha512-2jnZ0IkjZxvguITjFTrGiLyzQZcTvaw8DAaCXxZq/dsHXz7KfMQ3OUJy7Tz9vnRtZRVz6VRCPDvruvU8Ts44wQ=="}, "redis-parser": {"version": "2.6.0", "resolved": "https://registry.npmjs.org/redis-parser/-/redis-parser-2.6.0.tgz", "integrity": "sha1-Uu0J2srBCPGmMcB+m2mUHnoZUEs="}, "reduce-component": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/reduce-component/-/reduce-component-1.0.1.tgz", "integrity": "sha1-4Mk1QsV0UhvqE98PlIjtgqt3xdo="}, "redux": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/redux/-/redux-4.0.1.tgz", "integrity": "sha512-R7bAtSkk7nY6O/OYMVR9RiBI+XghjF9rlbl5806HJbQph0LJVHZrU5oaO4q70eUKiqMRqm4y07KLTlMZ2BlVmg==", "requires": {"loose-envify": "^1.4.0", "symbol-observable": "^1.2.0"}}, "referrer-policy": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/referrer-policy/-/referrer-policy-1.1.0.tgz", "integrity": "sha1-NXdOtzW/UPtsB46DM0tHI1AgfXk="}, "regex-not": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/regex-not/-/regex-not-1.0.2.tgz", "integrity": "sha512-J6SDjUgDxQj5NusnOtdFxDwN/+HWykR8GELwctJ7mdqhcyy1xEc4SRFHUXvxTp661YaVKAjfRLZ9cCqS6tn32A==", "requires": {"extend-shallow": "^3.0.2", "safe-regex": "^1.1.0"}}, "relative-microtime": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/relative-microtime/-/relative-microtime-2.0.0.tgz", "integrity": "sha512-l18ha6HEZc+No/uK4GyAnNxgKW7nvEe35IaeN54sShMojtqik2a6GbTyuiezkjpPaqP874Z3lW5ysBo5irz4NA=="}, "remove-trailing-separator": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz", "integrity": "sha1-wkvOKig62tW8P1jg1IJJuSN52O8="}, "repeat-element": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/repeat-element/-/repeat-element-1.1.3.tgz", "integrity": "sha512-ahGq0ZnV5m5XtZLMb+vP76kcAM5nkLqk0lpqAuojSKGgQtn4eRi4ZZGm2olo2zKFH+sMsWaqOCW1dqAnOru72g=="}, "repeat-string": {"version": "1.6.1", "resolved": "https://registry.npmjs.org/repeat-string/-/repeat-string-1.6.1.tgz", "integrity": "sha1-jcrkcOHIirwtYA//Sndihtp15jc="}, "request": {"version": "2.83.0", "resolved": "https://registry.npmjs.org/request/-/request-2.83.0.tgz", "integrity": "sha512-lR3gD69osqm6EYLk9wB/G1W/laGWjzH90t1vEa2xuxHD5KUrSzp9pUSfTm+YC5Nxt2T8nMPEvKlhbQayU7bgFw==", "requires": {"aws-sign2": "~0.7.0", "aws4": "^1.6.0", "caseless": "~0.12.0", "combined-stream": "~1.0.5", "extend": "~3.0.1", "forever-agent": "~0.6.1", "form-data": "~2.3.1", "har-validator": "~5.0.3", "hawk": "~6.0.2", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.17", "oauth-sign": "~0.8.2", "performance-now": "^2.1.0", "qs": "~6.5.1", "safe-buffer": "^5.1.1", "stringstream": "~0.0.5", "tough-cookie": "~2.3.3", "tunnel-agent": "^0.6.0", "uuid": "^3.1.0"}, "dependencies": {"ajv": {"version": "5.5.2", "resolved": "https://registry.npmjs.org/ajv/-/ajv-5.5.2.tgz", "integrity": "sha1-c7Xuyj+rZT49P5Qis0GtQiBdyWU=", "requires": {"co": "^4.6.0", "fast-deep-equal": "^1.0.0", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.3.0"}}, "fast-deep-equal": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-1.1.0.tgz", "integrity": "sha1-wFNHeBfIa1HaqFPIHgWbcz0CNhQ="}, "har-validator": {"version": "5.0.3", "resolved": "https://registry.npmjs.org/har-validator/-/har-validator-5.0.3.tgz", "integrity": "sha1-ukAsJmGU8VlW7xXg/PJCmT9qff0=", "requires": {"ajv": "^5.1.0", "har-schema": "^2.0.0"}}, "json-schema-traverse": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.3.1.tgz", "integrity": "sha1-NJptRMU6Ud6JtAgFxdXlm0F9M0A="}, "oauth-sign": {"version": "0.8.2", "resolved": "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.8.2.tgz", "integrity": "sha1-Rqarfwrq2N6unsBWV4C31O/rnUM="}, "punycode": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/punycode/-/punycode-1.4.1.tgz", "integrity": "sha1-wNWmOycYgArY4esPpSachN1BhF4="}, "tough-cookie": {"version": "2.3.4", "resolved": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.3.4.tgz", "integrity": "sha512-TZ6TTfI5NtZnuyy/Kecv+CnoROnyXn2DN97LontgQpCwsX2XyLYCC0ENhYkehSOwAp8rTQKc/NUIF7BkQ5rKLA==", "requires": {"punycode": "^1.4.1"}}}}, "request-promise": {"version": "4.2.2", "resolved": "https://registry.npmjs.org/request-promise/-/request-promise-4.2.2.tgz", "integrity": "sha1-0epG1lSm7k+O5qT+oQGMIpEZBLQ=", "requires": {"bluebird": "^3.5.0", "request-promise-core": "1.1.1", "stealthy-require": "^1.1.0", "tough-cookie": ">=2.3.3"}}, "request-promise-core": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/request-promise-core/-/request-promise-core-1.1.1.tgz", "integrity": "sha1-Pu4AssWqgyOc+wTFcA2jb4HNCLY=", "requires": {"lodash": "^4.13.1"}}, "require-ancestors": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/require-ancestors/-/require-ancestors-1.0.0.tgz", "integrity": "sha512-Nqeo9Gfp0KvnxTixnxLGEbThMAi+YYgnwRoigtOs1Oo3eGBYfqCd3dagq1vBCVVuc1EnIt3Eu1eGemwOOEZozw=="}, "require-in-the-middle": {"version": "5.0.3", "resolved": "https://registry.npmjs.org/require-in-the-middle/-/require-in-the-middle-5.0.3.tgz", "integrity": "sha512-p/ICV8uMlqC4tjOYabLMxAWCIKa0YUQgZZ6KDM0xgXJNgdGQ1WmL2A07TwmrZw+wi6ITUFKzH5v3n+ENEyXVkA==", "requires": {"debug": "^4.1.1", "module-details-from-path": "^1.0.3", "resolve": "^1.12.0"}}, "resolve": {"version": "1.17.0", "resolved": "https://registry.npmjs.org/resolve/-/resolve-1.17.0.tgz", "integrity": "sha512-ic+7JYiV8Vi2yzQGFWOkiZD5Z9z7O2Zhm9XMaTxdJExKasieFCr+yXZ/WmXsckHiKl12ar0y6XiXDx3m4RHn1w==", "requires": {"path-parse": "^1.0.6"}}, "resolve-url": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/resolve-url/-/resolve-url-0.2.1.tgz", "integrity": "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo="}, "ret": {"version": "0.1.15", "resolved": "https://registry.npmjs.org/ret/-/ret-0.1.15.tgz", "integrity": "sha512-TTlYpa+OL+vMMNG24xSlQGEJ3B/RzEfUlLct7b5G/ytav+wPrplCpVMFuwzXbkecJrb6IYo1iFb0S9v37754mg=="}, "retry-as-promised": {"version": "2.3.2", "resolved": "https://registry.npmjs.org/retry-as-promised/-/retry-as-promised-2.3.2.tgz", "integrity": "sha1-zZdO5P2bX+A8vzGHHuSCIcB3N7c=", "requires": {"bluebird": "^3.4.6", "debug": "^2.6.9"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "rimraf": {"version": "2.7.1", "resolved": "https://registry.npmjs.org/rimraf/-/rimraf-2.7.1.tgz", "integrity": "sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w==", "requires": {"glob": "^7.1.3"}}, "rttc": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/rttc/-/rttc-7.4.0.tgz", "integrity": "sha1-vJys1Grdkj3rYklaAZNOt+9hn7Q=", "requires": {"lodash": "^3.8.0"}, "dependencies": {"lodash": {"version": "3.10.1", "resolved": "https://registry.npmjs.org/lodash/-/lodash-3.10.1.tgz", "integrity": "sha1-W/Rejkm6QYnhfUgnid/RW9FAt7Y="}}}, "safe-buffer": {"version": "5.1.2", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g=="}, "safe-regex": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/safe-regex/-/safe-regex-1.1.0.tgz", "integrity": "sha1-QKNmnzsHfR6UPURinhV91IAjvy4=", "requires": {"ret": "~0.1.10"}}, "safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="}, "sax": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/sax/-/sax-1.2.1.tgz", "integrity": "sha1-e45lYZCyKOgaZq6nSEgNgozS03o="}, "seed-random": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/seed-random/-/seed-random-2.2.0.tgz", "integrity": "sha1-KpsZ4lCoFwmSMaW5mk2vgLf77VQ="}, "semver": {"version": "6.3.0", "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.0.tgz", "integrity": "sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw=="}, "send": {"version": "0.16.1", "resolved": "https://registry.npmjs.org/send/-/send-0.16.1.tgz", "integrity": "sha512-ElCLJdJIKPk6ux/Hocwhk7NFHpI3pVm/IZOYWqUmoxcgeyM+MpxHHKhb8QmlJDX1pU6WrgaHBkVNm73Sv7uc2A==", "requires": {"debug": "2.6.9", "depd": "~1.1.1", "destroy": "~1.0.4", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "~1.6.2", "mime": "1.4.1", "ms": "2.0.0", "on-finished": "~2.3.0", "range-parser": "~1.2.0", "statuses": "~1.3.1"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "sequelize": {"version": "4.37.7", "resolved": "https://registry.npmjs.org/sequelize/-/sequelize-4.37.7.tgz", "integrity": "sha512-1/M1Aua2GgejZbUI3T90G3uXXjcM4gTfFC36jGsepaJh3cRK9plPmlZeKkAQWWn4bCJaJozeEtuxfyPfQUY9wg==", "requires": {"bluebird": "^3.5.0", "cls-bluebird": "^2.1.0", "debug": "^3.1.0", "depd": "^1.1.0", "dottie": "^2.0.0", "generic-pool": "^3.4.0", "inflection": "1.12.0", "lodash": "^4.17.1", "moment": "^2.20.0", "moment-timezone": "^0.5.14", "retry-as-promised": "^2.3.2", "semver": "^5.5.0", "terraformer-wkt-parser": "^1.1.2", "toposort-class": "^1.0.1", "uuid": "^3.2.1", "validator": "^9.4.1", "wkx": "^0.4.1"}, "dependencies": {"debug": {"version": "3.2.6", "resolved": "https://registry.npmjs.org/debug/-/debug-3.2.6.tgz", "integrity": "sha512-mel+jf7nrtEl5Pn1Qx46zARXKDpBbvzezse7p7LqINmdoIk8PYP5SySaxEmYv6TZ0JyEKA1hsCId6DIhgITtWQ==", "requires": {"ms": "^2.1.1"}}, "generic-pool": {"version": "3.7.1", "resolved": "https://registry.npmjs.org/generic-pool/-/generic-pool-3.7.1.tgz", "integrity": "sha512-ug6DAZoNgWm6q5KhPFA+hzXfBLFQu5sTXxPpv44DmE0A2g+CiHoq9LTVdkXpZMkYVMoGw83F6W+WT0h0MFMK/w=="}, "semver": {"version": "5.7.1", "resolved": "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz", "integrity": "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ=="}}}, "serve-static": {"version": "1.13.1", "resolved": "https://registry.npmjs.org/serve-static/-/serve-static-1.13.1.tgz", "integrity": "sha512-hSMUZrsPa/I09VYFJwa627JJkNs0NrfL1Uzuup+GqHfToR2KcsXFymXSV90hoyw3M+msjFuQly+YzIH/q0MGlQ==", "requires": {"encodeurl": "~1.0.1", "escape-html": "~1.0.3", "parseurl": "~1.3.2", "send": "0.16.1"}}, "set-cookie-serde": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/set-cookie-serde/-/set-cookie-serde-1.0.0.tgz", "integrity": "sha512-Vq8e5GsupfJ7okHIvEPcfs5neCo7MZ1ZuWrO3sllYi3DOWt6bSSCpADzqXjz3k0fXehnoFIrmmhty9IN6U6BXQ=="}, "set-value": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/set-value/-/set-value-2.0.1.tgz", "integrity": "sha512-JxHc1weCN68wRY0fhCoXpyK55m/XPHafOmK4UWD7m2CI14GMcFypt4w/0+NV5f/ZMby2F6S2wwA7fgynh9gWSw==", "requires": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.3", "split-string": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}}}, "setprototypeof": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.1.0.tgz", "integrity": "sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ=="}, "shallow-clone-shim": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/shallow-clone-shim/-/shallow-clone-shim-2.0.0.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON><PERSON>diL3KGOoS67d73TEmk4tdPTO9GSMCoiphQsTcC9EtC+AOmMPjkyBkRoCJfW9ASsaZw1craaiw1dPN2D3Q=="}, "shelljs": {"version": "0.7.8", "resolved": "https://registry.npmjs.org/shelljs/-/shelljs-0.7.8.tgz", "integrity": "sha1-3svPh0sNHl+3LhSxZKloMEjprLM=", "requires": {"glob": "^7.0.0", "interpret": "^1.0.0", "rechoir": "^0.6.2"}}, "shimmer": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/shimmer/-/shimmer-1.2.1.tgz", "integrity": "sha512-sQTKC1Re/rM6XyFM6fIAGHRPVGvyXfgzIDvzoq608vM+jeyVD0Tu1E6Np0Kc2zAIFWIj963V2800iF/9LPieQw=="}, "should": {"version": "7.1.0", "resolved": "https://registry.npmjs.org/should/-/should-7.1.0.tgz", "integrity": "sha1-WUGumqZnW+JAZ9QywCryEMzZx3I=", "dev": true, "requires": {"should-equal": "0.5.0", "should-format": "0.3.0", "should-type": "0.2.0"}}, "should-equal": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/should-equal/-/should-equal-0.5.0.tgz", "integrity": "sha1-x5fxNfMGf+tp6+zbMGscP+IbPm8=", "dev": true, "requires": {"should-type": "0.2.0"}}, "should-format": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/should-format/-/should-format-0.3.0.tgz", "integrity": "sha1-QgB+wKochupEkUzJER8bnyfTzqw=", "dev": true, "requires": {"should-type": "0.2.0"}}, "should-type": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/should-type/-/should-type-0.2.0.tgz", "integrity": "sha1-ZwfvlVKdmJ3MCY/gdTqx+RNrt/Y=", "dev": true}, "simple-swizzle": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/simple-swizzle/-/simple-swizzle-0.2.2.tgz", "integrity": "sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=", "requires": {"is-arrayish": "^0.3.1"}, "dependencies": {"is-arrayish": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.3.2.tgz", "integrity": "sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ=="}}}, "slash": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/slash/-/slash-1.0.0.tgz", "integrity": "sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU="}, "snapdragon": {"version": "0.8.2", "resolved": "https://registry.npmjs.org/snapdragon/-/snapdragon-0.8.2.tgz", "integrity": "sha512-FtyOnWN/wCHTVXOMwvSv26d+ko5vWlIDD6zoUJ7LW8vh+ZBC8QdljveRP+crNrtBwioEUWy/4dMtbBjA4ioNlg==", "requires": {"base": "^0.11.1", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "map-cache": "^0.2.2", "source-map": "^0.5.6", "source-map-resolve": "^0.5.0", "use": "^3.1.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "requires": {"is-extendable": "^0.1.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "snapdragon-node": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/snapdragon-node/-/snapdragon-node-2.1.1.tgz", "integrity": "sha512-O27l4xaMYt/RSQ5TR3vpWCAB5Kb/czIcqUFOM/C4fYcLnbZUc1PkjTAMjof2pBWaSTwOUd6qUHcFGVGj7aIwnw==", "requires": {"define-property": "^1.0.0", "isobject": "^3.0.0", "snapdragon-util": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/define-property/-/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "requires": {"is-descriptor": "^1.0.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ==", "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ==", "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg==", "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "snapdragon-util": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/snapdragon-util/-/snapdragon-util-3.0.1.tgz", "integrity": "sha512-mbKkMdQKsjX4BAL4bRYTj21edOf8cN7XHdYUJEe+Zn99hVEYcMvKPct1IqNe7+AZPirn8BCDOQBHQZknqmKlZQ==", "requires": {"kind-of": "^3.2.0"}, "dependencies": {"is-buffer": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w=="}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "sntp": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/sntp/-/sntp-2.1.0.tgz", "integrity": "sha512-FL1b58BDrqS3A11lJ0zEdnJ3UOKqVxawAkF3k7F0CVN7VQ34aZrV+G8BZ1WC9ZL7NyrwsW0oviwsWDgRuVYtJg==", "requires": {"hoek": "4.x.x"}}, "socket-location": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/socket-location/-/socket-location-1.0.0.tgz", "integrity": "sha512-TwxpRM0pPE/3b24XQGLx8zq2J8kOwTy40FtiNC1KrWvl/Tsf7RYXruE9icecMhQwicXMo/HUJlGap8DNt2cgYw==", "requires": {"await-event": "^2.1.0"}}, "socket.io": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/socket.io/-/socket.io-2.0.4.tgz", "integrity": "sha1-waRZDO/4fs8TxyZS8Eb3FrKeYBQ=", "requires": {"debug": "~2.6.6", "engine.io": "~3.1.0", "socket.io-adapter": "~1.1.0", "socket.io-client": "2.0.4", "socket.io-parser": "~3.1.1"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "engine.io-client": {"version": "3.1.6", "resolved": "https://registry.npmjs.org/engine.io-client/-/engine.io-client-3.1.6.tgz", "integrity": "sha512-hnuHsFluXnsKOndS4Hv6SvUrgdYx1pk2NqfaDMW+GWdgfU3+/V25Cj7I8a0x92idSpa5PIhJRKxPvp9mnoLsfg==", "requires": {"component-emitter": "1.2.1", "component-inherit": "0.0.3", "debug": "~3.1.0", "engine.io-parser": "~2.1.1", "has-cors": "1.1.0", "indexof": "0.0.1", "parseqs": "0.0.5", "parseuri": "0.0.5", "ws": "~3.3.1", "xmlhttprequest-ssl": "~1.5.4", "yeast": "0.1.2"}, "dependencies": {"debug": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/debug/-/debug-3.1.0.tgz", "integrity": "sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==", "requires": {"ms": "2.0.0"}}}}, "isarray": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-2.0.1.tgz", "integrity": "sha1-o32U7ZzaLVmGXJ92/llu4fM4dB4="}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "socket.io-client": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/socket.io-client/-/socket.io-client-2.0.4.tgz", "integrity": "sha1-CRilUkBtxeVAs4Dc2Xr8SmQzL44=", "requires": {"backo2": "1.0.2", "base64-arraybuffer": "0.1.5", "component-bind": "1.0.0", "component-emitter": "1.2.1", "debug": "~2.6.4", "engine.io-client": "~3.1.0", "has-cors": "1.1.0", "indexof": "0.0.1", "object-component": "0.0.3", "parseqs": "0.0.5", "parseuri": "0.0.5", "socket.io-parser": "~3.1.1", "to-array": "0.1.4"}}, "socket.io-parser": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/socket.io-parser/-/socket.io-parser-3.1.3.tgz", "integrity": "sha512-g0a2HPqLguqAczs3dMECuA1RgoGFPyvDqcbaDEdCWY9g59kdUAz3YRmaJBNKXflrHNwB7Q12Gkf/0CZXfdHR7g==", "requires": {"component-emitter": "1.2.1", "debug": "~3.1.0", "has-binary2": "~1.0.2", "isarray": "2.0.1"}, "dependencies": {"debug": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/debug/-/debug-3.1.0.tgz", "integrity": "sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==", "requires": {"ms": "2.0.0"}}}}, "ws": {"version": "3.3.3", "resolved": "https://registry.npmjs.org/ws/-/ws-3.3.3.tgz", "integrity": "sha512-nnWLa/NwZSt4KQJu51MYlCcSQ5g7INpOrOMt4XV8j4dqTXdmlUmSHQ8/oLC069ckre0fRsgfvsKwbTdtKLCDkA==", "requires": {"async-limiter": "~1.0.0", "safe-buffer": "~5.1.0", "ultron": "~1.1.0"}}}}, "socket.io-adapter": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/socket.io-adapter/-/socket.io-adapter-1.1.2.tgz", "integrity": "sha512-WzZRUj1kUjrTIrUKpZLEzFZ1OLj5FwLlAFQs9kuZJzJi5DKdU7FsWc36SNmA8iDOtwBQyT8FkrriRM8vXLYz8g=="}, "socket.io-client": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/socket.io-client/-/socket.io-client-2.2.0.tgz", "integrity": "sha512-56ZrkTDbdTLmBIyfFYesgOxsjcLnwAKoN4CiPyTVkMQj3zTUh0QAx3GbvIvLpFEOvQWu92yyWICxB0u7wkVbYA==", "requires": {"backo2": "1.0.2", "base64-arraybuffer": "0.1.5", "component-bind": "1.0.0", "component-emitter": "1.2.1", "debug": "~3.1.0", "engine.io-client": "~3.3.1", "has-binary2": "~1.0.2", "has-cors": "1.1.0", "indexof": "0.0.1", "object-component": "0.0.3", "parseqs": "0.0.5", "parseuri": "0.0.5", "socket.io-parser": "~3.3.0", "to-array": "0.1.4"}, "dependencies": {"debug": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/debug/-/debug-3.1.0.tgz", "integrity": "sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "socket.io-emitter": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/socket.io-emitter/-/socket.io-emitter-3.1.1.tgz", "integrity": "sha1-sF+CAG5pvD63QoeUysO7zUuqDMc=", "requires": {"debug": "~3.1.0", "notepack.io": "~2.1.0", "redis": "2.6.3", "socket.io-parser": "3.1.2"}, "dependencies": {"debug": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/debug/-/debug-3.1.0.tgz", "integrity": "sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==", "requires": {"ms": "2.0.0"}}, "isarray": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-2.0.1.tgz", "integrity": "sha1-o32U7ZzaLVmGXJ92/llu4fM4dB4="}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "redis": {"version": "2.6.3", "resolved": "https://registry.npmjs.org/redis/-/redis-2.6.3.tgz", "integrity": "sha1-hDBbklU8ah8Jx8R8MLEazn27etQ=", "requires": {"double-ended-queue": "^2.1.0-0", "redis-commands": "^1.2.0", "redis-parser": "^2.0.0"}}, "socket.io-parser": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/socket.io-parser/-/socket.io-parser-3.1.2.tgz", "integrity": "sha1-28IoIVH8T6675Aru3Ady66YZ9/I=", "requires": {"component-emitter": "1.2.1", "debug": "~2.6.4", "has-binary2": "~1.0.2", "isarray": "2.0.1"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}}}}}, "socket.io-parser": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/socket.io-parser/-/socket.io-parser-3.3.0.tgz", "integrity": "sha512-hczmV6bDgdaEbVqhAeVMM/jfUfzuEZHsQg6eOmLgJht6G3mPKMxYm75w2+qhAQZ+4X+1+ATZ+QFKeOZD5riHng==", "requires": {"component-emitter": "1.2.1", "debug": "~3.1.0", "isarray": "2.0.1"}, "dependencies": {"debug": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/debug/-/debug-3.1.0.tgz", "integrity": "sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==", "requires": {"ms": "2.0.0"}}, "isarray": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-2.0.1.tgz", "integrity": "sha1-o32U7ZzaLVmGXJ92/llu4fM4dB4="}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "socket.io-redis": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/socket.io-redis/-/socket.io-redis-5.2.0.tgz", "integrity": "sha1-j+KtlEX8UIhvtwq8dZ1nQD1Ymd8=", "requires": {"debug": "~2.6.8", "notepack.io": "~2.1.2", "redis": "~2.8.0", "socket.io-adapter": "~1.1.0", "uid2": "0.0.3"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "source-map": {"version": "0.5.7", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="}, "source-map-resolve": {"version": "0.5.3", "resolved": "https://registry.npmjs.org/source-map-resolve/-/source-map-resolve-0.5.3.tgz", "integrity": "sha512-Htz+RnsXWk5+P2slx5Jh3Q66vhQj1Cllm0zvnaY98+NFx+Dv2CF/f5O/t8x+KaNdrdIAsruNzoh/KpialbqAnw==", "requires": {"atob": "^2.1.2", "decode-uri-component": "^0.2.0", "resolve-url": "^0.2.1", "source-map-url": "^0.4.0", "urix": "^0.1.0"}}, "source-map-support": {"version": "0.5.19", "resolved": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.19.tgz", "integrity": "sha512-Wonm7zOCIJzBGQdB+thsPar0kYuCIzYvxZwlBa87yi/Mdjv7Tip2cyVbLj5o0cFPN4EVkuTwb3GDDyUx2DGnGw==", "requires": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="}}}, "source-map-url": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/source-map-url/-/source-map-url-0.4.0.tgz", "integrity": "sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM="}, "spark-md5": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/spark-md5/-/spark-md5-1.0.1.tgz", "integrity": "sha1-xLmo1Bz3sIRUI6ghgk+N/6D1G3w="}, "spdx-correct": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/spdx-correct/-/spdx-correct-3.1.1.tgz", "integrity": "sha512-cOYcUWwhCuHCXi49RhFRCyJEK3iPj1Ziz9DpViV3tbZOwXD49QzIN3MpOLJNxh2qwq2lJJZaKMVw9qNi4jTC0w==", "requires": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "spdx-exceptions": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/spdx-exceptions/-/spdx-exceptions-2.3.0.tgz", "integrity": "sha512-/tTrYOC7PPI1nUAgx34hUpqXuyJG+DTHJTnIULG4rDygi4xu/tfgmq1e1cIRwRzwZgo4NLySi+ricLkZkw4i5A=="}, "spdx-expression-parse": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz", "integrity": "sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==", "requires": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "spdx-license-ids": {"version": "3.0.5", "resolved": "https://registry.npmjs.org/spdx-license-ids/-/spdx-license-ids-3.0.5.tgz", "integrity": "sha512-J+FWzZoynJEXGphVIS+XEh3kFSjZX/1i9gFBaWQcB+/tmpe2qUsSBABpcxqxnAxFdiUFEgAX1bjYGQvIZmoz9Q=="}, "split": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/split/-/split-1.0.1.tgz", "integrity": "sha512-mTyOoPbrivtXnwnIxZRFYRrPNtEFKlpB2fvjSnCQUiAA6qAZzqwna5envK4uk6OIeP17CsdF3rSBGYVBsU0Tkg==", "requires": {"through": "2"}}, "split-string": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/split-string/-/split-string-3.1.0.tgz", "integrity": "sha512-NzNVhJDYpwceVVii8/Hu6DKfD2G+NrQHlS/V/qgv763EYudVwEcMQNxd2lh+0VrUByXN/oJkl5grOhYWvQUYiw==", "requires": {"extend-shallow": "^3.0.0"}}, "sprintf-js": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.1.2.tgz", "integrity": "sha512-VE0SOVEHCk7Qc8ulkWw3ntAzXuqf7S2lvwQaDLRnUeIEaKNQJzV6BwmLKhOqT61aGhfUMrXeaBk+oDGCzvhcug=="}, "sql-summary": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/sql-summary/-/sql-summary-1.0.1.tgz", "integrity": "sha512-IpCr2tpnNkP3Jera4ncexsZUp0enJBLr+pHCyTweMUBrbJsTgQeLWx1FXLhoBj/MvcnUQpkgOn2EY8FKOkUzww=="}, "sshpk": {"version": "1.16.1", "resolved": "https://registry.npmjs.org/sshpk/-/sshpk-1.16.1.tgz", "integrity": "sha512-HXXqVUq7+pcKeLqqZj6mHFUMvXtOJt1uoUx09pFW6011inTMxqI8BA8PM95myrIyyKwdnzjdFjLiE6KBPVtJIg==", "requires": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}, "dependencies": {"jsbn": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/jsbn/-/jsbn-0.1.1.tgz", "integrity": "sha1-peZUwuWi3rXyAdls77yoDA7y9RM="}}}, "stack-trace": {"version": "0.0.10", "resolved": "https://registry.npmjs.org/stack-trace/-/stack-trace-0.0.10.tgz", "integrity": "sha1-VHxws0fo0ytOEI6hoqFZ5f3eGcA="}, "stackframe": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/stackframe/-/stackframe-1.2.0.tgz", "integrity": "sha512-GrdeshiRmS1YLMYgzF16olf2jJ/IzxXY9lhKOskuVziubpTYcYqyOwYeJKzQkwy7uN0fYSsbsC4RQaXf9LCrYA=="}, "stackman": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/stackman/-/stackman-4.0.1.tgz", "integrity": "sha512-lntIge3BFEElgvpZT2ld5f4U+mF84fRtJ8vA3ymUVx1euVx43ZMkd09+5RWW4FmvYDFhZwPh1gvtdsdnJyF4Fg==", "requires": {"after-all-results": "^2.0.0", "async-cache": "^1.1.0", "debug": "^4.1.1", "error-callsites": "^2.0.3", "load-source-map": "^1.0.0"}}, "static-extend": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/static-extend/-/static-extend-0.1.2.tgz", "integrity": "sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=", "requires": {"define-property": "^0.2.5", "object-copy": "^0.1.0"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "requires": {"is-descriptor": "^0.1.0"}}}}, "statuses": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/statuses/-/statuses-1.3.1.tgz", "integrity": "sha1-+vUbnrdKrvOzrPStX2Gr8ky3uT4="}, "stealthy-require": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/stealthy-require/-/stealthy-require-1.1.1.tgz", "integrity": "sha1-NbCYdbT/SfJqd35QmzCQoyJr8ks="}, "stream-chopper": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/stream-chopper/-/stream-chopper-3.0.1.tgz", "integrity": "sha512-f7h+ly8baAE26iIjcp3VbnBkbIRGtrvV0X0xxFM/d7fwLTYnLzDPTXRKNxa2HZzohOrc96NTrR+FaV3mzOelNA==", "requires": {"readable-stream": "^3.0.6"}}, "streamsearch": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/streamsearch/-/streamsearch-0.1.2.tgz", "integrity": "sha1-gIudDlb8Jz2Am6VzOOkpkZoanxo="}, "string": {"version": "3.3.3", "resolved": "https://registry.npmjs.org/string/-/string-3.3.3.tgz", "integrity": "sha1-XqIRzZLSKOGEKUmQpsyXs2anfLA="}, "string.prototype.trimend": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/string.prototype.trimend/-/string.prototype.trimend-1.0.1.tgz", "integrity": "sha512-LRPxFUaTtpqYsTeNKaFOw3R4bxIzWOnbQ837QfBylo8jIxtcbK/A/sMV7Q+OAV/vWo+7s25pOE10KYSjaSO06g==", "requires": {"define-properties": "^1.1.3", "es-abstract": "^1.17.5"}}, "string.prototype.trimstart": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/string.prototype.trimstart/-/string.prototype.trimstart-1.0.1.tgz", "integrity": "sha512-XxZn+QpvrBI1FOcg6dIpxUPgWCPuNXvMD72aaRaUQv1eD4e/Qy8i/hFTe0BUmD60p/QA6bh1avmuPTfNjqVWRw==", "requires": {"define-properties": "^1.1.3", "es-abstract": "^1.17.5"}}, "string_decoder": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz", "integrity": "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==", "requires": {"safe-buffer": "~5.2.0"}, "dependencies": {"safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ=="}}}, "stringstream": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/stringstream/-/stringstream-0.0.6.tgz", "integrity": "sha512-87GEBAkegbBcweToUrdzf3eLhWNg06FJTebl4BVJz/JgWy8CvEr9dRtX5qWphiynMSQlxxi+QqN0z5T32SLlhA=="}, "strip-ansi": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "requires": {"ansi-regex": "^2.0.0"}}, "superagent": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/superagent/-/superagent-1.8.5.tgz", "integrity": "sha1-HA3cOvMOgOuE68BcshItqP6UC1U=", "requires": {"component-emitter": "~1.2.0", "cookiejar": "2.0.6", "debug": "2", "extend": "3.0.0", "form-data": "1.0.0-rc3", "formidable": "~1.0.14", "methods": "~1.1.1", "mime": "1.3.4", "qs": "2.3.3", "readable-stream": "1.0.27-1", "reduce-component": "1.0.1"}, "dependencies": {"async": {"version": "1.5.2", "resolved": "https://registry.npmjs.org/async/-/async-1.5.2.tgz", "integrity": "sha1-7GphrlZIDAw8skHJVhjiCJL5Zyo="}, "cookiejar": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/cookiejar/-/cookiejar-2.0.6.tgz", "integrity": "sha1-Cr81atANHFohnYjURRgEbdAmrP4="}, "debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "extend": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/extend/-/extend-3.0.0.tgz", "integrity": "sha1-WkdDU7nzNT3dgXbf03uRyDpG8dQ="}, "form-data": {"version": "1.0.0-rc3", "resolved": "https://registry.npmjs.org/form-data/-/form-data-1.0.0-rc3.tgz", "integrity": "sha1-01vGLn+8KTeuePlIqqDTjZBgdXc=", "requires": {"async": "^1.4.0", "combined-stream": "^1.0.5", "mime-types": "^2.1.3"}}, "formidable": {"version": "1.0.16", "resolved": "https://registry.npmjs.org/formidable/-/formidable-1.0.16.tgz", "integrity": "sha1-SRbP38TL7QILJXpqlQWpqzjCzQ4="}, "isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="}, "mime": {"version": "1.3.4", "resolved": "https://registry.npmjs.org/mime/-/mime-1.3.4.tgz", "integrity": "sha1-EV+eO2s9rylZmDyzjxSaLUDrXVM="}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "qs": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/qs/-/qs-2.3.3.tgz", "integrity": "sha1-6eha2+ddoLvkyOBHaghikPhjtAQ="}, "readable-stream": {"version": "1.0.27-1", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.27-1.tgz", "integrity": "sha1-a2eYPCA1fO/QfwFlABoW1xDZEHg=", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ="}}}, "supertest": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/supertest/-/supertest-1.0.0.tgz", "integrity": "sha1-0QxZv0uab43x6jAOAiIfHWrgke0=", "dev": true, "requires": {"methods": "1.x", "superagent": "~1.2.0"}, "dependencies": {"async": {"version": "0.9.2", "resolved": "https://registry.npmjs.org/async/-/async-0.9.2.tgz", "integrity": "sha1-rqdNXmHB+JlhO/ZL2mbUx48v0X0=", "dev": true}, "combined-stream": {"version": "0.0.7", "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-0.0.7.tgz", "integrity": "sha1-ATfmV7qlp1QcV6w3rF/AfXO03B8=", "dev": true, "requires": {"delayed-stream": "0.0.5"}}, "component-emitter": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/component-emitter/-/component-emitter-1.1.2.tgz", "integrity": "sha1-KWWU8nU9qmOZbSrwjRWpURbJrsM=", "dev": true}, "cookiejar": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/cookiejar/-/cookiejar-2.0.1.tgz", "integrity": "sha1-PRJ1L2rfaKiS8zJDNJK9WBK7Zo8=", "dev": true}, "debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dev": true, "requires": {"ms": "2.0.0"}}, "delayed-stream": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-0.0.5.tgz", "integrity": "sha1-1LH0OpPoKW3+AmlPRoC8N6MTxz8=", "dev": true}, "extend": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/extend/-/extend-1.2.1.tgz", "integrity": "sha1-oPX9bPyDpf5J72mNYOyKYk3UV2w=", "dev": true}, "form-data": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/form-data/-/form-data-0.2.0.tgz", "integrity": "sha1-Jvi8JtpkQOKZy9z7aQNcT3em5GY=", "dev": true, "requires": {"async": "~0.9.0", "combined-stream": "~0.0.4", "mime-types": "~2.0.3"}}, "formidable": {"version": "1.0.14", "resolved": "https://registry.npmjs.org/formidable/-/formidable-1.0.14.tgz", "integrity": "sha1-Kz9MQRy7X91pXESEPiojUUpDIxo=", "dev": true}, "isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=", "dev": true}, "mime": {"version": "1.3.4", "resolved": "https://registry.npmjs.org/mime/-/mime-1.3.4.tgz", "integrity": "sha1-EV+eO2s9rylZmDyzjxSaLUDrXVM=", "dev": true}, "mime-db": {"version": "1.12.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.12.0.tgz", "integrity": "sha1-PQxjGA9FjrENMlqqN9fFiuMS6dc=", "dev": true}, "mime-types": {"version": "2.0.14", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.0.14.tgz", "integrity": "sha1-MQ4VnbI+B3+Lsit0jav6SVcUCqY=", "dev": true, "requires": {"mime-db": "~1.12.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}, "qs": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/qs/-/qs-2.3.3.tgz", "integrity": "sha1-6eha2+ddoLvkyOBHaghikPhjtAQ=", "dev": true}, "readable-stream": {"version": "1.0.27-1", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.27-1.tgz", "integrity": "sha1-a2eYPCA1fO/QfwFlABoW1xDZEHg=", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=", "dev": true}, "superagent": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/superagent/-/superagent-1.2.0.tgz", "integrity": "sha1-qsJiUzwexVOBRKETcc028kSldP0=", "dev": true, "requires": {"component-emitter": "1.1.2", "cookiejar": "2.0.1", "debug": "2", "extend": "1.2.1", "form-data": "0.2.0", "formidable": "1.0.14", "methods": "1.0.1", "mime": "1.3.4", "qs": "2.3.3", "readable-stream": "1.0.27-1", "reduce-component": "1.0.1"}, "dependencies": {"methods": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/methods/-/methods-1.0.1.tgz", "integrity": "sha1-dbyRlD3/19oDfPPusO1zoAN80Us=", "dev": true}}}}}, "supports-color": {"version": "5.5.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz", "integrity": "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==", "requires": {"has-flag": "^3.0.0"}}, "swagger-converter": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/swagger-converter/-/swagger-converter-0.1.7.tgz", "integrity": "sha1-oJdRnG8e5N1n4wjZtT3cnCslf5c=", "requires": {"lodash.clonedeep": "^2.4.1"}}, "swagger-express-mw": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/swagger-express-mw/-/swagger-express-mw-0.1.0.tgz", "integrity": "sha1-seT7WgGVnO8imRoSHq5JSt0w5To=", "requires": {"swagger-node-runner": "^0.5.0"}}, "swagger-node-runner": {"version": "0.5.13", "resolved": "https://registry.npmjs.org/swagger-node-runner/-/swagger-node-runner-0.5.13.tgz", "integrity": "sha1-VwwCCq55e9Waqyr2kXK/XWE3aKQ=", "requires": {"bagpipes": "^0.0.6", "config": "^1.16.0", "cors": "^2.5.3", "debug": "^2.1.3", "js-yaml": "^3.3.0", "lodash": "^3.6.0", "swagger-tools": "^0.9.7"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "lodash": {"version": "3.10.1", "resolved": "https://registry.npmjs.org/lodash/-/lodash-3.10.1.tgz", "integrity": "sha1-W/Rejkm6QYnhfUgnid/RW9FAt7Y="}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "swagger-tools": {"version": "0.9.16", "resolved": "https://registry.npmjs.org/swagger-tools/-/swagger-tools-0.9.16.tgz", "integrity": "sha1-45+uPVgdcTaCSR4ZJs2Hvywgm/s=", "requires": {"async": "^1.3.0", "body-parser": "1.12.4", "commander": "^2.8.1", "debug": "^2.2.0", "js-yaml": "^3.3.1", "json-refs": "^2.1.5", "lodash-compat": "^3.10.0", "multer": "^1.1.0", "parseurl": "^1.3.0", "path-to-regexp": "^1.2.0", "qs": "^4.0.0", "serve-static": "^1.10.0", "spark-md5": "^1.0.0", "string": "^3.3.0", "superagent": "^1.2.0", "swagger-converter": "^0.1.7", "traverse": "^0.6.6", "z-schema": "^3.15.4"}, "dependencies": {"async": {"version": "1.5.2", "resolved": "https://registry.npmjs.org/async/-/async-1.5.2.tgz", "integrity": "sha1-7GphrlZIDAw8skHJVhjiCJL5Zyo="}, "body-parser": {"version": "1.12.4", "resolved": "https://registry.npmjs.org/body-parser/-/body-parser-1.12.4.tgz", "integrity": "sha1-CQcAxLoohiqFIO83g5X97l9hwik=", "requires": {"bytes": "1.0.0", "content-type": "~1.0.1", "debug": "~2.2.0", "depd": "~1.0.1", "iconv-lite": "0.4.8", "on-finished": "~2.2.1", "qs": "2.4.2", "raw-body": "~2.0.1", "type-is": "~1.6.2"}, "dependencies": {"debug": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/debug/-/debug-2.2.0.tgz", "integrity": "sha1-+HBX6ZWxofauaklgZkE3vFbwOdo=", "requires": {"ms": "0.7.1"}}, "qs": {"version": "2.4.2", "resolved": "https://registry.npmjs.org/qs/-/qs-2.4.2.tgz", "integrity": "sha1-9854jld33wtQENp/fE5zujJHD1o="}}}, "bytes": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/bytes/-/bytes-1.0.0.tgz", "integrity": "sha1-NWnt6Lo0MV+rmcPpLLBMciDeH6g="}, "debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}, "dependencies": {"ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "depd": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/depd/-/depd-1.0.1.tgz", "integrity": "sha1-gK7GTJ1tl+ZcwqnKqTwKpqv3Oqo="}, "ee-first": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.0.tgz", "integrity": "sha1-ag18YiHkkP7v2S7D9EHJzozQl/Q="}, "iconv-lite": {"version": "0.4.8", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.8.tgz", "integrity": "sha1-xgGadZXyzvynAuq2lKAQvNkpjSA="}, "isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="}, "ms": {"version": "0.7.1", "resolved": "https://registry.npmjs.org/ms/-/ms-0.7.1.tgz", "integrity": "sha1-nNE8A62/8ltl7/3nzoZO6VIBcJg="}, "on-finished": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/on-finished/-/on-finished-2.2.1.tgz", "integrity": "sha1-XIXBzDYpn3gCllP2Z/J7a5nrwCk=", "requires": {"ee-first": "1.1.0"}}, "path-to-regexp": {"version": "1.8.0", "resolved": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-1.8.0.tgz", "integrity": "sha512-n43JRhlUKUAlibEJhPeir1ncUID16QnEjNpwzNdO3Lm4ywrBpBZ5oLD0I6br9evr1Y9JTqwRtAh7JLoOzAQdVA==", "requires": {"isarray": "0.0.1"}}, "qs": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/qs/-/qs-4.0.0.tgz", "integrity": "sha1-wx2bdOwn33XlQ6hseHKO2NRiNgc="}, "raw-body": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/raw-body/-/raw-body-2.0.2.tgz", "integrity": "sha1-osL5jIUxzumcY9jSOLfel7tln8o=", "requires": {"bytes": "2.1.0", "iconv-lite": "0.4.8"}, "dependencies": {"bytes": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/bytes/-/bytes-2.1.0.tgz", "integrity": "sha1-rJPEEOL/ycx89LRks4KJBn9eR7Q="}}}}}, "switchback": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/switchback/-/switchback-2.0.5.tgz", "integrity": "sha512-w9gnsTxR5geOKt45QUryhDP9KTLcOAqje9usR2VQ2ng8DfhaF+mkIcArxioMP/p6Z/ecKE58i2/B0DDlMJK1jw==", "requires": {"@sailshq/lodash": "^3.10.3"}}, "symbol-observable": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/symbol-observable/-/symbol-observable-1.2.0.tgz", "integrity": "sha512-e900nM8RRtGhlV36KGEU9k65K3mPb1WV70OdjfxlG2EAuM1noi/E/BaW/uMhL7bPEssK8QV57vN3esixjUvcXQ=="}, "terraformer": {"version": "1.0.12", "resolved": "https://registry.npmjs.org/terraformer/-/terraformer-1.0.12.tgz", "integrity": "sha512-MokUp0+MFal4CmJDVL6VAO1bKegeXcBM2RnPVfqcFIp2IIv8EbPAjG0j/vEy/vuKB8NVMMSF2vfpVS/QLe4DBg==", "requires": {"@types/geojson": "^7946.0.0 || ^1.0.0"}}, "terraformer-wkt-parser": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/terraformer-wkt-parser/-/terraformer-wkt-parser-1.2.1.tgz", "integrity": "sha512-+CJyNLWb3lJ9RsZMTM66BY0MT3yIo4l4l22Jd9CrZuwzk54fsu4Sc7zejuS9fCITTuTQy3p06d4MZMVI7v5wSg==", "requires": {"@types/geojson": "^1.0.0", "terraformer": "~1.0.5"}}, "text-hex": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/text-hex/-/text-hex-1.0.0.tgz", "integrity": "sha512-uuVGNWzgJ4yhRaNSiubPY7OjISw4sw4E5Uv0wbjp+OzcbmVU/rsT8ujgcXJhn9ypzsgr5vlzpPqP+MBBKcGvbg=="}, "thirty-two": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/thirty-two/-/thirty-two-0.0.2.tgz", "integrity": "sha1-QlPinYywWPBIAmfFaYwOSSflS2o="}, "through": {"version": "2.3.8", "resolved": "https://registry.npmjs.org/through/-/through-2.3.8.tgz", "integrity": "sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU="}, "tiny-emitter": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/tiny-emitter/-/tiny-emitter-2.0.2.tgz", "integrity": "sha512-2NM0auVBGft5tee/OxP4PI3d8WItkDM+fPnaRAVo6xTDI2knbz9eC5ArWGqtGlYqiH3RU5yMpdyTTO7MguC4ow=="}, "to-array": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/to-array/-/to-array-0.1.4.tgz", "integrity": "sha1-F+bBH3PdTz10zaek/zI46a2b+JA="}, "to-object-path": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/to-object-path/-/to-object-path-0.3.0.tgz", "integrity": "sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=", "requires": {"kind-of": "^3.0.2"}, "dependencies": {"is-buffer": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w=="}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "requires": {"is-buffer": "^1.1.5"}}}}, "to-regex": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/to-regex/-/to-regex-3.0.2.tgz", "integrity": "sha512-FWtleNAtZ/Ki2qtqej2CXTOayOH9bHDQF+Q48VpWyDXjbYxA4Yz8iDB31zXOBUlOHHKidDbqGVrTUvQMPmBGBw==", "requires": {"define-property": "^2.0.2", "extend-shallow": "^3.0.2", "regex-not": "^1.0.2", "safe-regex": "^1.1.0"}}, "to-regex-range": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-2.1.1.tgz", "integrity": "sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=", "requires": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}}, "to-source-code": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/to-source-code/-/to-source-code-1.0.2.tgz", "integrity": "sha1-3RNr2x4dvYC76s8IiZJnjpBwv+o=", "requires": {"is-nil": "^1.0.0"}}, "topo": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/topo/-/topo-1.1.0.tgz", "integrity": "sha1-6ddRYV0buH3IZdsYL6HKCl71NtU=", "requires": {"hoek": "2.x.x"}, "dependencies": {"hoek": {"version": "2.16.3", "resolved": "https://registry.npmjs.org/hoek/-/hoek-2.16.3.tgz", "integrity": "sha1-ILt0A9POo5jpHcRxCo/xuCdKJe0="}}}, "toposort-class": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/toposort-class/-/toposort-class-1.0.1.tgz", "integrity": "sha1-f/0feMi+KMO6Rc1OGj9e4ZO9mYg="}, "tough-cookie": {"version": "2.4.3", "resolved": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.4.3.tgz", "integrity": "sha512-Q5srk/4vDM54WJsJio3XNn6K2sCG+CQ8G5Wz6bZhRZoAe/+TxjWB/GlFAnYEbkYVlON9FMk/fE3h2RLpPXo4lQ==", "requires": {"psl": "^1.1.24", "punycode": "^1.4.1"}, "dependencies": {"punycode": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/punycode/-/punycode-1.4.1.tgz", "integrity": "sha1-wNWmOycYgArY4esPpSachN1BhF4="}}}, "traceparent": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/traceparent/-/traceparent-1.0.0.tgz", "integrity": "sha512-b/hAbgx57pANQ6cg2eBguY3oxD6FGVLI1CC2qoi01RmHR7AYpQHPXTig9FkzbWohEsVuHENZHP09aXuw3/LM+w==", "requires": {"random-poly-fill": "^1.0.1"}}, "traverse": {"version": "0.6.6", "resolved": "https://registry.npmjs.org/traverse/-/traverse-0.6.6.tgz", "integrity": "sha1-y99WD9e5r2MlAv7UD5GMFX6pcTc="}, "triple-beam": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/triple-beam/-/triple-beam-1.3.0.tgz", "integrity": "sha512-XrHUvV5HpdLmIj4uVMxHggLbFSZYIn7HEWsqePZcI50pco+MPqJ50wMGY794X7AOOhxOBAjbkqfAbEe/QMp2Lw=="}, "tunnel-agent": {"version": "0.6.0", "resolved": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.6.0.tgz", "integrity": "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=", "requires": {"safe-buffer": "^5.0.1"}}, "tv4": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/tv4/-/tv4-1.3.0.tgz", "integrity": "sha1-0CDIRvrdUMhVq7JeuuzGj8EPeWM="}, "tweetnacl": {"version": "0.14.5", "resolved": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.5.tgz", "integrity": "sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q="}, "type-detect": {"version": "4.0.8", "resolved": "https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz", "integrity": "sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g=="}, "type-fest": {"version": "0.8.1", "resolved": "https://registry.npmjs.org/type-fest/-/type-fest-0.8.1.tgz", "integrity": "sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA=="}, "type-is": {"version": "1.6.18", "resolved": "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz", "integrity": "sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==", "requires": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}}, "typed-function": {"version": "0.10.7", "resolved": "https://registry.npmjs.org/typed-function/-/typed-function-0.10.7.tgz", "integrity": "sha512-3mlZ5AwRMbLvUKkc8a1TI4RUJUS2H27pmD5q0lHRObgsoWzhDAX01yg82kwSP1FUw922/4Y9ZliIEh0qJZcz+g=="}, "typedarray": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz", "integrity": "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c="}, "tz-offset": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/tz-offset/-/tz-offset-0.0.1.tgz", "integrity": "sha512-kMBmblijHJXyOpKzgDhKx9INYU4u4E1RPMB0HqmKSgWG8vEcf3exEfLh4FFfzd3xdQOw9EuIy/cP0akY6rHopQ=="}, "uid2": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/uid2/-/uid2-0.0.3.tgz", "integrity": "sha1-SDEm4Rd03y9xuLY53NeZw3YWK4I="}, "ultron": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/ultron/-/ultron-1.1.1.tgz", "integrity": "sha512-UIEXBNeYmKptWH6z8ZnqTeS8fV74zG0/eRU9VGkpzz+LIJNs8W/zM/L+7ctCkRrgbNnnR0xxw4bKOr0cW0N0Og=="}, "underscore": {"version": "1.11.0", "resolved": "https://registry.npmjs.org/underscore/-/underscore-1.11.0.tgz", "integrity": "sha512-xY96SsN3NA461qIRKZ/+qox37YXPtSBswMGfiNptr+wrt6ds4HaMw23TP612fEyGekRE6LNRiLYr/aqbHXNedw=="}, "unicode-byte-truncate": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/unicode-byte-truncate/-/unicode-byte-truncate-1.0.0.tgz", "integrity": "sha1-qm8PNHUZP+IMMgrJIT425i6HZKc=", "requires": {"is-integer": "^1.0.6", "unicode-substring": "^0.1.0"}}, "unicode-substring": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/unicode-substring/-/unicode-substring-0.1.0.tgz", "integrity": "sha1-YSDOPDkDhdvND2DDK5BlxBgdSzY="}, "union-value": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/union-value/-/union-value-1.0.1.tgz", "integrity": "sha512-tJfXmxMeWYnczCVs7XAEvIV7ieppALdyepWMkHkwciRpZraG/xwT+s2JN8+pr1+8jCRf80FFzvr+MpQeeoF4Xg==", "requires": {"arr-union": "^3.1.0", "get-value": "^2.0.6", "is-extendable": "^0.1.1", "set-value": "^2.0.1"}}, "unpipe": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw="}, "unset-value": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/unset-value/-/unset-value-1.0.0.tgz", "integrity": "sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=", "requires": {"has-value": "^0.3.1", "isobject": "^3.0.0"}, "dependencies": {"has-value": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/has-value/-/has-value-0.3.1.tgz", "integrity": "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=", "requires": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}, "dependencies": {"isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "requires": {"isarray": "1.0.0"}}}}, "has-values": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/has-values/-/has-values-0.1.4.tgz", "integrity": "sha1-bWHeldkd/Km5oCCJrThL/49it3E="}}}, "upath": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/upath/-/upath-1.2.0.tgz", "integrity": "sha512-aZwGpamFO61g3OlfT7OQCHqhGnW43ieH9WZeP7QxN/G/jS4jfqUkZxoryvJgVPEcrl5NL/ggHsSmLMHuH64Lhg=="}, "uri-js": {"version": "4.4.0", "resolved": "https://registry.npmjs.org/uri-js/-/uri-js-4.4.0.tgz", "integrity": "sha512-B0yRTzYdUCCn9n+F4+Gh4yIDtMQcaJsmYBDsTSG8g/OejKBodLQ2IHfN3bM7jUsRXndopT7OIXWdYqc1fjmV6g==", "requires": {"punycode": "^2.1.0"}, "dependencies": {"punycode": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/punycode/-/punycode-2.1.1.tgz", "integrity": "sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A=="}}}, "urix": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/urix/-/urix-0.1.0.tgz", "integrity": "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI="}, "url": {"version": "0.10.3", "resolved": "https://registry.npmjs.org/url/-/url-0.10.3.tgz", "integrity": "sha1-Ah5NnHcF8hu/N9A861h2dAJ3TGQ=", "requires": {"punycode": "1.3.2", "querystring": "0.2.0"}}, "use": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/use/-/use-3.1.1.tgz", "integrity": "sha512-cwESVXlO3url9YWlFW/TA9cshCEhtu7IKJ/p5soJ/gGpj7vbvFrAY/eIioQ6Dw23KjZhYgiIo8HOs1nQ2vr/oQ=="}, "util-deprecate": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="}, "utils-merge": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz", "integrity": "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM="}, "uuid": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/uuid/-/uuid-3.2.1.tgz", "integrity": "sha512-jZnMwlb9Iku/O3smGWvZhauCf6cvvpKi4BKRiliS3cxnI+Gz9j5MEpTz2UFuXiKPJocb7gnsLHwiS05ige5BEA=="}, "uws": {"version": "9.14.0", "resolved": "https://registry.npmjs.org/uws/-/uws-9.14.0.tgz", "integrity": "sha512-HNMztPP5A1sKuVFmdZ6BPVpBQd5bUjNC8EFMFiICK+oho/OQsAJy5hnIx4btMHiOk8j04f/DbIlqnEZ9d72dqg==", "optional": true}, "v8-compile-cache": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/v8-compile-cache/-/v8-compile-cache-1.1.2.tgz", "integrity": "sha512-ejdrifsIydN1XDH7EuR2hn8ZrkRKUYF7tUcBjBy/lhrCvs2K+zRlbW9UHc0IQ9RsYFZJFqJrieoIHfkCa0DBRA=="}, "validate-npm-package-license": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz", "integrity": "sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==", "requires": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "validator": {"version": "9.4.1", "resolved": "https://registry.npmjs.org/validator/-/validator-9.4.1.tgz", "integrity": "sha512-YV5KjzvRmSyJ1ee/Dm5UED0G+1L4GZnLN3w6/T+zZm8scVua4sOhYKWTUrKa0H/tMiJyO9QLHMPN+9mB/aMunA=="}, "vary": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw="}, "verror": {"version": "1.10.0", "resolved": "https://registry.npmjs.org/verror/-/verror-1.10.0.tgz", "integrity": "sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=", "requires": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "vizion": {"version": "0.2.13", "resolved": "https://registry.npmjs.org/vizion/-/vizion-0.2.13.tgz", "integrity": "sha1-ExTN7is0EW+fWxJIU2+V2/zW718=", "requires": {"async": "1.5"}, "dependencies": {"async": {"version": "1.5.2", "resolved": "https://registry.npmjs.org/async/-/async-1.5.2.tgz", "integrity": "sha1-7GphrlZIDAw8skHJVhjiCJL5Zyo="}}}, "vxx": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/vxx/-/vxx-1.2.2.tgz", "integrity": "sha1-dB+1HG8R0zg9pvm5IBil17qAdhE=", "requires": {"continuation-local-storage": "^3.1.4", "debug": "^2.6.3", "extend": "^3.0.0", "is": "^3.2.0", "lodash.findindex": "^4.4.0", "lodash.isequal": "^4.0.0", "lodash.merge": "^4.6.0", "methods": "^1.1.1", "semver": "^5.0.1", "shimmer": "^1.0.0", "uuid": "^3.0.1"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "semver": {"version": "5.7.1", "resolved": "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz", "integrity": "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ=="}}}, "winston": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/winston/-/winston-3.2.1.tgz", "integrity": "sha512-zU6vgnS9dAWCEKg/QYigd6cgMVVNwyTzKs81XZtTFuRwJOcDdBg7AU0mXVyNbs7O5RH2zdv+BdNZUlx7mXPuOw==", "requires": {"async": "^2.6.1", "diagnostics": "^1.1.1", "is-stream": "^1.1.0", "logform": "^2.1.1", "one-time": "0.0.4", "readable-stream": "^3.1.1", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "winston-transport": "^4.3.0"}}, "winston-compat": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/winston-compat/-/winston-compat-0.1.5.tgz", "integrity": "sha512-EPvPcHT604AV3Ji6d3+vX8ENKIml9VSxMRnPQ+cuK/FX6f3hvPP2hxyoeeCOCFvDrJEujalfcKWlWPvAnFyS9g==", "requires": {"cycle": "~1.0.3", "logform": "^1.6.0", "triple-beam": "^1.2.0"}, "dependencies": {"fecha": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/fecha/-/fecha-2.3.3.tgz", "integrity": "sha512-lUGBnIamTAwk4znq5BcqsDaxSmZ9nDVJaij6NvRt/Tg4R69gERA+otPKbS86ROw9nxVMw2/mp1fnaiWqbs6Sdg=="}, "logform": {"version": "1.10.0", "resolved": "https://registry.npmjs.org/logform/-/logform-1.10.0.tgz", "integrity": "sha512-em5ojIhU18fIMOw/333mD+ZLE2fis0EzXl1ZwHx4iQzmpQi6odNiY/t+ITNr33JZhT9/KEaH+UPIipr6a9EjWg==", "requires": {"colors": "^1.2.1", "fast-safe-stringify": "^2.0.4", "fecha": "^2.3.3", "ms": "^2.1.1", "triple-beam": "^1.2.0"}}}}, "winston-elasticsearch-apm": {"version": "0.0.7", "resolved": "https://registry.npmjs.org/winston-elasticsearch-apm/-/winston-elasticsearch-apm-0.0.7.tgz", "integrity": "sha512-Bae5BkM36/6x2TN7zv5Qu7qQF2SnUDhba26b0kCL6+A3yaTyOa9sjf4sakoWx27Lil7M6OCicC39rfcIKNkHHg==", "requires": {"lodash": "^4.17.11", "lodash.defaultsdeep": "^4.6.0", "winston-compat": "^0.1.4", "winston-transport": "^4.2.0"}, "dependencies": {"lodash": {"version": "4.17.20", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.20.tgz", "integrity": "sha512-PlhdFcillOINfeV7Ni6oF1TAEayyZBoZ8bcshTHqOYJYlrqzRK5hagpagky5o4HfCzzd1TRkXPMFq6cKk9rGmA=="}}}, "winston-transport": {"version": "4.4.0", "resolved": "https://registry.npmjs.org/winston-transport/-/winston-transport-4.4.0.tgz", "integrity": "sha512-Lc7/p3GtqtqPBYYtS6KCN3c77/2QCev51DvcJKbkFPQNoj1sinkGwLGFDxkXY9J6p9+EPnYs+D90uwbnaiURTw==", "requires": {"readable-stream": "^2.3.7", "triple-beam": "^1.2.0"}, "dependencies": {"readable-stream": {"version": "2.3.7", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.7.tgz", "integrity": "sha512-<PERSON>bho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw==", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==", "requires": {"safe-buffer": "~5.1.0"}}}}, "wkx": {"version": "0.4.8", "resolved": "https://registry.npmjs.org/wkx/-/wkx-0.4.8.tgz", "integrity": "sha512-ikPXMM9IR/gy/LwiOSqWlSL3X/J5uk9EO2hHNRXS41eTLXaUFEVw9fn/593jW/tE5tedNg8YjT5HkCa4FqQZyQ==", "requires": {"@types/node": "*"}}, "wrappy": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="}, "ws": {"version": "6.1.4", "resolved": "https://registry.npmjs.org/ws/-/ws-6.1.4.tgz", "integrity": "sha512-eqZfL+NE/YQc1/ZynhojeV8q+H050oR8AZ2uIev7RU10svA9ZnJUddHcOUZTJLinZ9yEfdA2kSATS2qZK5fhJA==", "requires": {"async-limiter": "~1.0.0"}}, "x-xss-protection": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/x-xss-protection/-/x-xss-protection-1.1.0.tgz", "integrity": "sha512-rx3GzJlgEeZ08MIcDsU2vY2B1QEriUKJTSiNHHUIem6eg9pzVOr2TL3Y4Pd6TMAM5D5azGjcxqI62piITBDHVg=="}, "xml2js": {"version": "0.4.17", "resolved": "https://registry.npmjs.org/xml2js/-/xml2js-0.4.17.tgz", "integrity": "sha1-F76T6q4/O3eTWceVtBlwWogX6Gg=", "requires": {"sax": ">=0.6.0", "xmlbuilder": "^4.1.0"}}, "xmlbuilder": {"version": "4.2.1", "resolved": "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-4.2.1.tgz", "integrity": "sha1-qlijBBoGb5DqoWwvU4n/GfP0YaU=", "requires": {"lodash": "^4.0.0"}}, "xmlhttprequest-ssl": {"version": "1.5.5", "resolved": "https://registry.npmjs.org/xmlhttprequest-ssl/-/xmlhttprequest-ssl-1.5.5.tgz", "integrity": "sha1-wodrBhaKrcQOV9l+gRkayPQ5iz4="}, "xtend": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/xtend/-/xtend-4.0.2.tgz", "integrity": "sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ=="}, "yallist": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/yallist/-/yallist-2.1.2.tgz", "integrity": "sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI="}, "yamljs": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/yamljs/-/yamljs-0.3.0.tgz", "integrity": "sha512-C/FsVVhht4iPQYXOInoxUM/1ELSf9EsgKH34FofQOp6hwCPrW4vG4w5++TED3xRUo8gD7l0P1J1dLlDYzODsTQ==", "requires": {"argparse": "^1.0.7", "glob": "^7.0.5"}}, "yauzl": {"version": "2.10.0", "resolved": "https://registry.npmjs.org/yauzl/-/yauzl-2.10.0.tgz", "integrity": "sha1-x+sXyT4RLLEIb6bY5R+wZnt5pfk=", "requires": {"buffer-crc32": "~0.2.3", "fd-slicer": "~1.1.0"}}, "yeast": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/yeast/-/yeast-0.1.2.tgz", "integrity": "sha1-AI4G2AlDIMNy28L47XagymyKxBk="}, "z-schema": {"version": "3.25.1", "resolved": "https://registry.npmjs.org/z-schema/-/z-schema-3.25.1.tgz", "integrity": "sha512-7tDlwhrBG+oYFdXNOjILSurpfQyuVgkRe3hB2q8TEssamDHB7BbLWYkYO98nTn0FibfdFroFKDjndbgufAgS/Q==", "requires": {"commander": "^2.7.1", "core-js": "^2.5.7", "lodash.get": "^4.0.0", "lodash.isequal": "^4.0.0", "validator": "^10.0.0"}, "dependencies": {"validator": {"version": "10.11.0", "resolved": "https://registry.npmjs.org/validator/-/validator-10.11.0.tgz", "integrity": "sha512-X/p3UZerAIsbBfN/IwahhYaBbY68EN/UQBWHtsbXGT5bfrH/p4NQzUCG1kF/rtKaNpnJ7jAu6NGTdSNtyNIXMw=="}}}}}