###############################
#### SECRET CONFIGURATIONS ####
###############################

HOLLAEX_SECRET_ACTIVATION_CODE=

########################################
#### ADVANCED SECRET CONFIGURATIONS ####
########################################

HOLLAEX_SECRET_REDIS_HOST=$ENVIRONMENT_EXCHANGE_NAME-redis
HOLLAEX_SECRET_REDIS_PORT=6379
HOLLAEX_SECRET_REDIS_PASSWORD=

HOLLAEX_SECRET_PUBSUB_HOST=$ENVIRONMENT_EXCHANGE_NAME-redis
HOLLAEX_SECRET_PUBSUB_PORT=6379
HOLLAEX_SECRET_PUBSUB_PASSWORD=

HOLLAEX_SECRET_DB_NAME=hollaex
HOLLAEX_SECRET_DB_USERNAME=hollaex
HOLLAEX_SECRET_DB_PASSWORD=
HOLLAEX_SECRET_DB_HOST=$ENVIRONMENT_EXCHANGE_NAME-db
HOLLAEX_SECRET_DB_PORT=5432

ENVIRONMENT_KUBERNETES_DOCKER_REGISTRY_HOST=docker.io
ENVIRONMENT_KUBERNETES_DOCKER_REGISTRY_USERNAME=
ENVIRONMENT_KUBERNETES_DOCKER_REGISTRY_PASSWORD=
ENVIRONMENT_KUBERNETES_DOCKER_REGISTRY_EMAIL=

ENVIRONMENT_KUBERNETES_S3_BACKUP_CRONJOB_ACCESSKEY=
ENVIRONMENT_KUBERNETES_S3_BACKUP_CRONJOB_SECRETKEY=

HOLLAEX_SECRET_API_KEY=
HOLLAEX_SECRET_API_SECRET=

HOLLAEX_SECRET_SMTP_PASSWORD=

######################################################################
#### AUTOMATICALLY GENERATED PASSWORDS GONNA BE STORED DOWN BELOW ####
######################################################################

HOLLAEX_SECRET_SUPERVISOR_PASSWORD=
HOLLAEX_SECRET_SUPPORT_PASSWORD=
HOLLAEX_SECRET_KYC_PASSWORD=
HOLLAEX_SECRET_QUICK_TRADE_SECRET=
HOLLAEX_SECRET_SECRET=