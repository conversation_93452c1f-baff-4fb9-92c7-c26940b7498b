{"version": "2.2.2", "private": false, "description": "HollaEx Kit", "keywords": ["swagger", "api", "exchange", "bitcoin", "cryptocurrency", "<PERSON><PERSON>la", "holla<PERSON>"], "author": "bitHolla Inc.", "license": "bitHolla Inc.", "main": "app.js", "dependencies": {"JSONStream": "1.3.5", "bcryptjs": "2.4.3", "bluebird": "3.5.3", "body-parser": "1.19.0", "chai": "4.2.0", "cors": "2.8.5", "elastic-apm-node": "3.15.0", "eval": "0.1.4", "expect-ct": "0.1.0", "express": "4.16.2", "express-validator": "6.7.0", "flat": "5.0.0", "geoip-lite": "1.4.1", "helmet": "3.12.0", "hollaex-node-lib": "github:bitholla/hollaex-node-lib#2.10", "hollaex-tools-lib": "github:bitholla/hollaex-tools-lib#2.14", "http": "0.0.0", "install": "0.10.4", "json2csv": "4.5.4", "jsonwebtoken": "7.4.3", "latest-version": "5.1.0", "lodash": "4.17.20", "mathjs": "3.20.2", "moment": "2.21.0", "moment-timezone": "0.5.28", "morgan": "1.9.0", "multer": "1.4.2", "node-cron": "2.0.3", "nodemailer": "6.4.6", "npm": "5.7.1", "npm-programmatic": "0.0.12", "otp": "0.1.3", "pg": "6.4.2", "pg-hstore": "2.3.2", "pg-query-stream": "4.1.0", "pm2": "2.10.1", "pmx": "1.6.4", "random-string": "0.2.0", "redis": "2.8.0", "redux": "4.0.1", "request": "2.83.0", "request-promise": "4.2.2", "sequelize": "4.37.7", "swagger-express-mw": "0.1.0", "swagger-ui-express": "4.0.7", "uglify-es": "3.3.9", "umzug": "2.3.0", "uuid": "3.2.1", "validator": "9.4.1", "winston": "3.2.1", "winston-elasticsearch-apm": "0.0.7", "ws-heartbeat": "1.1.0", "yamljs": "0.3.0"}, "devDependencies": {"chai": "4.2.0", "mocha": "3.5.3", "nyc": "11.5.0", "should": "7.1.0", "supertest": "1.0.0"}, "scripts": {"undomigrate": "sequelize db:migrate:undo:all", "migrate": "sequelize db:migrate", "start": "node app.js", "test": "NODE_ENV=test nyc --reporter=html --reporter=text mocha --recursive --timeout 5000"}, "nyc": {"exclude": ["db", "config", "test", "app.js", "constants.js"]}}